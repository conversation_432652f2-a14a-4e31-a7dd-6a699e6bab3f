-- ===== CAR AUCTION DATABASE CREATION SCRIPT =====
-- This script creates the complete database structure for the Car Auction Website

-- Create the database (if using SQL Server, not LocalDB)
-- CREATE DATABASE CarAuctionDB;
-- GO
-- USE CarAuctionDB;
-- GO

-- ===== USERS TABLE =====
CREATE TABLE Users (
    UserId INT IDENTITY(1,1) PRIMARY KEY,
    FirstName NVARCHAR(50) NOT NULL,
    LastName NVARCHAR(50) NOT NULL,
    Username NVARCHAR(50) UNIQUE NOT NULL,
    Email NVARCHAR(100) UNIQUE NOT NULL,
    Phone NVARCHAR(15) NOT NULL,
    PasswordHash NVARCHAR(255) NOT NULL,
    CreatedDate DATETIME2 DEFAULT GETDATE(),
    LastLoginDate DATETIME2 NULL,
    IsActive BIT DEFAULT 1,
    ProfileImageUrl NVARCHAR(255) NULL,
    Address NVARCHAR(255) NULL,
    City NVARCHAR(50) NULL,
    
    INDEX IX_Users_Username (Username),
    INDEX IX_Users_Email (Email),
    INDEX IX_Users_IsActive (IsActive)
);

-- ===== CARS TABLE =====
CREATE TABLE Cars (
    CarId INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT NOT NULL,
    Brand NVARCHAR(50) NOT NULL,
    Model NVARCHAR(50) NOT NULL,
    Year INT NOT NULL,
    Mileage INT NOT NULL,
    FuelType NVARCHAR(20) NOT NULL, -- Gasoline, Diesel, Hybrid, Electric
    Transmission NVARCHAR(20) NOT NULL, -- Manual, Automatic, CVT
    Color NVARCHAR(30) NOT NULL,
    EngineSize DECIMAL(3,1) NULL,
    Condition NVARCHAR(20) NOT NULL, -- Excellent, Very Good, Good, Fair, Poor
    Description NTEXT NOT NULL,
    Features NVARCHAR(MAX) NULL, -- JSON string of features
    ImageUrl NVARCHAR(255) NULL,
    ContactPhone NVARCHAR(15) NOT NULL,
    Location NVARCHAR(100) NOT NULL,
    CreatedDate DATETIME2 DEFAULT GETDATE(),
    IsActive BIT DEFAULT 1,
    
    FOREIGN KEY (UserId) REFERENCES Users(UserId),
    INDEX IX_Cars_Brand (Brand),
    INDEX IX_Cars_Year (Year),
    INDEX IX_Cars_UserId (UserId),
    INDEX IX_Cars_IsActive (IsActive)
);

-- ===== AUCTIONS TABLE =====
CREATE TABLE Auctions (
    AuctionId INT IDENTITY(1,1) PRIMARY KEY,
    CarId INT NOT NULL,
    StartingPrice DECIMAL(10,2) NOT NULL,
    ReservePrice DECIMAL(10,2) NULL,
    CurrentBid DECIMAL(10,2) NOT NULL,
    StartDateTime DATETIME2 NOT NULL,
    EndDateTime DATETIME2 NOT NULL,
    Status NVARCHAR(20) DEFAULT 'Pending', -- Pending, Active, Completed, Cancelled
    WinnerUserId INT NULL,
    CreatedDate DATETIME2 DEFAULT GETDATE(),
    ApprovedDate DATETIME2 NULL,
    ApprovedBy INT NULL,
    
    FOREIGN KEY (CarId) REFERENCES Cars(CarId),
    FOREIGN KEY (WinnerUserId) REFERENCES Users(UserId),
    FOREIGN KEY (ApprovedBy) REFERENCES Users(UserId),
    INDEX IX_Auctions_Status (Status),
    INDEX IX_Auctions_EndDateTime (EndDateTime),
    INDEX IX_Auctions_CarId (CarId)
);

-- ===== BIDS TABLE =====
CREATE TABLE Bids (
    BidId INT IDENTITY(1,1) PRIMARY KEY,
    AuctionId INT NOT NULL,
    UserId INT NOT NULL,
    BidAmount DECIMAL(10,2) NOT NULL,
    BidDateTime DATETIME2 DEFAULT GETDATE(),
    IsWinning BIT DEFAULT 0,
    
    FOREIGN KEY (AuctionId) REFERENCES Auctions(AuctionId),
    FOREIGN KEY (UserId) REFERENCES Users(UserId),
    INDEX IX_Bids_AuctionId (AuctionId),
    INDEX IX_Bids_UserId (UserId),
    INDEX IX_Bids_BidDateTime (BidDateTime)
);

-- ===== CAR IMAGES TABLE =====
CREATE TABLE CarImages (
    ImageId INT IDENTITY(1,1) PRIMARY KEY,
    CarId INT NOT NULL,
    ImageUrl NVARCHAR(255) NOT NULL,
    ImageOrder INT DEFAULT 1,
    IsMain BIT DEFAULT 0,
    UploadedDate DATETIME2 DEFAULT GETDATE(),
    
    FOREIGN KEY (CarId) REFERENCES Cars(CarId),
    INDEX IX_CarImages_CarId (CarId)
);

-- ===== WATCHLIST TABLE =====
CREATE TABLE Watchlist (
    WatchlistId INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT NOT NULL,
    AuctionId INT NOT NULL,
    AddedDate DATETIME2 DEFAULT GETDATE(),
    
    FOREIGN KEY (UserId) REFERENCES Users(UserId),
    FOREIGN KEY (AuctionId) REFERENCES Auctions(AuctionId),
    UNIQUE (UserId, AuctionId),
    INDEX IX_Watchlist_UserId (UserId)
);

-- ===== NOTIFICATIONS TABLE =====
CREATE TABLE Notifications (
    NotificationId INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT NOT NULL,
    Title NVARCHAR(100) NOT NULL,
    Message NVARCHAR(500) NOT NULL,
    Type NVARCHAR(20) NOT NULL, -- Bid, Auction, System
    IsRead BIT DEFAULT 0,
    CreatedDate DATETIME2 DEFAULT GETDATE(),
    RelatedAuctionId INT NULL,
    
    FOREIGN KEY (UserId) REFERENCES Users(UserId),
    FOREIGN KEY (RelatedAuctionId) REFERENCES Auctions(AuctionId),
    INDEX IX_Notifications_UserId (UserId),
    INDEX IX_Notifications_IsRead (IsRead)
);

-- ===== SYSTEM SETTINGS TABLE =====
CREATE TABLE SystemSettings (
    SettingId INT IDENTITY(1,1) PRIMARY KEY,
    SettingKey NVARCHAR(50) UNIQUE NOT NULL,
    SettingValue NVARCHAR(255) NOT NULL,
    Description NVARCHAR(255) NULL,
    UpdatedDate DATETIME2 DEFAULT GETDATE(),
    UpdatedBy INT NULL,
    
    FOREIGN KEY (UpdatedBy) REFERENCES Users(UserId)
);

-- ===== AUDIT LOG TABLE =====
CREATE TABLE AuditLog (
    LogId INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT NULL,
    Action NVARCHAR(50) NOT NULL,
    TableName NVARCHAR(50) NOT NULL,
    RecordId INT NOT NULL,
    OldValues NVARCHAR(MAX) NULL,
    NewValues NVARCHAR(MAX) NULL,
    LogDateTime DATETIME2 DEFAULT GETDATE(),
    IPAddress NVARCHAR(45) NULL,
    
    FOREIGN KEY (UserId) REFERENCES Users(UserId),
    INDEX IX_AuditLog_UserId (UserId),
    INDEX IX_AuditLog_LogDateTime (LogDateTime)
);

-- ===== INSERT DEFAULT SYSTEM SETTINGS =====
INSERT INTO SystemSettings (SettingKey, SettingValue, Description) VALUES
('SiteName', 'مزادات السيارات', 'اسم الموقع'),
('SiteEmail', '<EMAIL>', 'البريد الإلكتروني للموقع'),
('SitePhone', '+966501234567', 'رقم هاتف الموقع'),
('MaxAuctionDays', '14', 'أقصى مدة للمزاد بالأيام'),
('MinBidIncrement', '100', 'أقل زيادة في المزايدة'),
('CommissionRate', '5', 'نسبة العمولة بالمئة'),
('AutoExtendMinutes', '5', 'دقائق التمديد التلقائي'),
('MaxImagesPerCar', '10', 'أقصى عدد صور لكل سيارة'),
('RequireApproval', '1', 'يتطلب موافقة المدير على المزادات'),
('MaintenanceMode', '0', 'وضع الصيانة');

-- ===== CREATE VIEWS =====

-- View for Active Auctions with Car Details
CREATE VIEW vw_ActiveAuctions AS
SELECT 
    a.AuctionId,
    a.StartingPrice,
    a.CurrentBid,
    a.EndDateTime,
    a.Status,
    c.Brand,
    c.Model,
    c.Year,
    c.Mileage,
    c.FuelType,
    c.Color,
    c.ImageUrl,
    c.Location,
    u.FirstName + ' ' + u.LastName AS SellerName,
    u.Phone AS SellerPhone,
    (SELECT COUNT(*) FROM Bids b WHERE b.AuctionId = a.AuctionId) AS BidCount,
    (SELECT TOP 1 BidAmount FROM Bids b WHERE b.AuctionId = a.AuctionId ORDER BY BidAmount DESC) AS HighestBid
FROM Auctions a
INNER JOIN Cars c ON a.CarId = c.CarId
INNER JOIN Users u ON c.UserId = u.UserId
WHERE a.Status = 'Active' AND a.EndDateTime > GETDATE();

-- View for User Auction History
CREATE VIEW vw_UserAuctionHistory AS
SELECT 
    u.UserId,
    u.Username,
    a.AuctionId,
    c.Brand + ' ' + c.Model + ' ' + CAST(c.Year AS VARCHAR) AS CarDescription,
    a.StartingPrice,
    a.CurrentBid,
    a.Status,
    a.EndDateTime,
    CASE 
        WHEN a.WinnerUserId = u.UserId THEN 'فائز'
        WHEN EXISTS (SELECT 1 FROM Bids b WHERE b.AuctionId = a.AuctionId AND b.UserId = u.UserId) THEN 'مشارك'
        ELSE 'مراقب'
    END AS ParticipationType
FROM Users u
LEFT JOIN Bids b ON u.UserId = b.UserId
LEFT JOIN Auctions a ON b.AuctionId = a.AuctionId
LEFT JOIN Cars c ON a.CarId = c.CarId
WHERE a.AuctionId IS NOT NULL;

-- ===== CREATE STORED PROCEDURES =====

-- Procedure to Place a Bid
CREATE PROCEDURE sp_PlaceBid
    @AuctionId INT,
    @UserId INT,
    @BidAmount DECIMAL(10,2)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @CurrentBid DECIMAL(10,2);
    DECLARE @EndDateTime DATETIME2;
    DECLARE @Status NVARCHAR(20);
    DECLARE @MinIncrement DECIMAL(10,2);
    
    -- Get current auction details
    SELECT @CurrentBid = CurrentBid, @EndDateTime = EndDateTime, @Status = Status
    FROM Auctions WHERE AuctionId = @AuctionId;
    
    -- Get minimum bid increment
    SELECT @MinIncrement = CAST(SettingValue AS DECIMAL(10,2))
    FROM SystemSettings WHERE SettingKey = 'MinBidIncrement';
    
    -- Validate bid
    IF @Status != 'Active'
    BEGIN
        RAISERROR('المزاد غير نشط', 16, 1);
        RETURN;
    END
    
    IF @EndDateTime <= GETDATE()
    BEGIN
        RAISERROR('انتهى وقت المزاد', 16, 1);
        RETURN;
    END
    
    IF @BidAmount <= @CurrentBid + @MinIncrement
    BEGIN
        RAISERROR('مبلغ المزايدة يجب أن يكون أكبر من السعر الحالي', 16, 1);
        RETURN;
    END
    
    BEGIN TRANSACTION;
    
    TRY
        -- Mark previous bids as not winning
        UPDATE Bids SET IsWinning = 0 WHERE AuctionId = @AuctionId;
        
        -- Insert new bid
        INSERT INTO Bids (AuctionId, UserId, BidAmount, IsWinning)
        VALUES (@AuctionId, @UserId, @BidAmount, 1);
        
        -- Update auction current bid
        UPDATE Auctions SET CurrentBid = @BidAmount WHERE AuctionId = @AuctionId;
        
        -- Auto-extend if bid is placed in last 5 minutes
        IF DATEDIFF(MINUTE, GETDATE(), @EndDateTime) <= 5
        BEGIN
            UPDATE Auctions 
            SET EndDateTime = DATEADD(MINUTE, 5, @EndDateTime)
            WHERE AuctionId = @AuctionId;
        END
        
        COMMIT TRANSACTION;
        
        SELECT 'تم تسجيل المزايدة بنجاح' AS Message;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END;

-- Procedure to End Expired Auctions
CREATE PROCEDURE sp_EndExpiredAuctions
AS
BEGIN
    SET NOCOUNT ON;
    
    UPDATE Auctions 
    SET Status = 'Completed',
        WinnerUserId = (
            SELECT TOP 1 UserId 
            FROM Bids 
            WHERE Bids.AuctionId = Auctions.AuctionId 
            ORDER BY BidAmount DESC
        )
    WHERE Status = 'Active' 
    AND EndDateTime <= GETDATE()
    AND AuctionId IN (SELECT AuctionId FROM Bids);
    
    -- Mark auctions with no bids as completed without winner
    UPDATE Auctions 
    SET Status = 'Completed'
    WHERE Status = 'Active' 
    AND EndDateTime <= GETDATE()
    AND AuctionId NOT IN (SELECT DISTINCT AuctionId FROM Bids);
    
    SELECT @@ROWCOUNT AS AuctionsEnded;
END;

-- ===== CREATE TRIGGERS =====

-- Trigger to log auction changes
CREATE TRIGGER tr_AuctionAudit
ON Auctions
AFTER INSERT, UPDATE, DELETE
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Log insertions
    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS (SELECT * FROM deleted)
    BEGIN
        INSERT INTO AuditLog (Action, TableName, RecordId, NewValues)
        SELECT 'INSERT', 'Auctions', AuctionId, 
               'StartingPrice: ' + CAST(StartingPrice AS VARCHAR) + 
               ', CurrentBid: ' + CAST(CurrentBid AS VARCHAR) +
               ', Status: ' + Status
        FROM inserted;
    END
    
    -- Log updates
    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        INSERT INTO AuditLog (Action, TableName, RecordId, OldValues, NewValues)
        SELECT 'UPDATE', 'Auctions', i.AuctionId,
               'Status: ' + d.Status + ', CurrentBid: ' + CAST(d.CurrentBid AS VARCHAR),
               'Status: ' + i.Status + ', CurrentBid: ' + CAST(i.CurrentBid AS VARCHAR)
        FROM inserted i
        INNER JOIN deleted d ON i.AuctionId = d.AuctionId;
    END
    
    -- Log deletions
    IF NOT EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        INSERT INTO AuditLog (Action, TableName, RecordId, OldValues)
        SELECT 'DELETE', 'Auctions', AuctionId,
               'StartingPrice: ' + CAST(StartingPrice AS VARCHAR) + 
               ', Status: ' + Status
        FROM deleted;
    END
END;

-- ===== CREATE INDEXES FOR PERFORMANCE =====
CREATE INDEX IX_Bids_AuctionId_BidAmount ON Bids (AuctionId, BidAmount DESC);
CREATE INDEX IX_Auctions_Status_EndDateTime ON Auctions (Status, EndDateTime);
CREATE INDEX IX_Cars_Brand_Model_Year ON Cars (Brand, Model, Year);

-- ===== SAMPLE DATA FOR TESTING =====

-- Insert sample users
INSERT INTO Users (FirstName, LastName, Username, Email, Phone, PasswordHash) VALUES
('أحمد', 'محمد', 'ahmed123', '<EMAIL>', '0501234567', 'hashed_password_1'),
('فاطمة', 'علي', 'fatima456', '<EMAIL>', '0507654321', 'hashed_password_2'),
('محمد', 'السعد', 'mohammed789', '<EMAIL>', '0509876543', 'hashed_password_3');

-- Insert sample cars
INSERT INTO Cars (UserId, Brand, Model, Year, Mileage, FuelType, Transmission, Color, Condition, Description, ContactPhone, Location) VALUES
(1, 'Toyota', 'Camry', 2020, 45000, 'Gasoline', 'Automatic', 'White', 'Excellent', 'سيارة في حالة ممتازة، صيانة دورية منتظمة', '0501234567', 'الرياض'),
(2, 'Honda', 'Accord', 2019, 60000, 'Gasoline', 'Automatic', 'Black', 'Very Good', 'سيارة نظيفة جداً، بدون حوادث', '0507654321', 'جدة'),
(3, 'BMW', 'X5', 2021, 25000, 'Gasoline', 'Automatic', 'Blue', 'Excellent', 'سيارة فاخرة في حالة ممتازة', '0509876543', 'الدمام');

-- Insert sample auctions
INSERT INTO Auctions (CarId, StartingPrice, CurrentBid, StartDateTime, EndDateTime, Status) VALUES
(1, 85000, 85000, GETDATE(), DATEADD(DAY, 7, GETDATE()), 'Active'),
(2, 75000, 78000, GETDATE(), DATEADD(DAY, 5, GETDATE()), 'Active'),
(3, 180000, 180000, GETDATE(), DATEADD(DAY, 10, GETDATE()), 'Active');

-- Insert sample bids
INSERT INTO Bids (AuctionId, UserId, BidAmount, IsWinning) VALUES
(2, 1, 76000, 0),
(2, 3, 78000, 1);

PRINT 'Database created successfully with sample data!';
PRINT 'You can now run the ASP.NET application.';
