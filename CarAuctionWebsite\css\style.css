/* ===== GLOBAL STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* ===== ARABIC FONT SUPPORT ===== */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

html {
    font-size: 16px;
}

/* Better Arabic text rendering */
* {
    text-rendering: optimizeLegibility;
    -webkit-font-feature-settings: "liga", "kern";
    font-feature-settings: "liga", "kern";
}

body {
    font-family: 'Cairo', 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
    direction: rtl;
    text-align: right;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    margin-bottom: 1rem;
    font-weight: 600;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
    color: #666;
}

a {
    color: #007bff;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #0056b3;
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d, #545b62);
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #545b62, #3d4449);
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #1e7e34, #155724);
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: #007bff;
    border: 2px solid #007bff;
}

.btn-outline:hover {
    background: #007bff;
    color: white;
}

.btn-full {
    width: 100%;
}

.btn-large {
    padding: 16px 32px;
    font-size: 18px;
}

.btn-small {
    padding: 8px 16px;
    font-size: 14px;
    min-width: 80px;
}

.btn-register {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white !important;
    padding: 10px 20px;
    border-radius: 25px;
}

.btn-register:hover {
    background: linear-gradient(135deg, #1e7e34, #155724);
    color: white !important;
}

/* ===== HEADER & NAVIGATION ===== */
.header {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-logo h2 {
    color: white;
    margin: 0;
    font-size: 1.8rem;
}

.nav-logo a {
    color: white;
    text-decoration: none;
}

.nav-logo i {
    color: #007bff;
    margin-left: 10px;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 2rem;
}

.nav-item {
    position: relative;
}

.nav-link {
    color: #e0e0e0;
    text-decoration: none;
    font-weight: 500;
    padding: 10px 15px;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: block;
}

.nav-link:hover,
.nav-link.active {
    color: white;
    background: rgba(0, 123, 255, 0.2);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: white;
    margin: 3px 0;
    transition: 0.3s;
}

/* ===== HERO SECTION ===== */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 100px 0;
    display: flex;
    align-items: center;
    min-height: 600px;
}

.hero-content {
    flex: 1;
    max-width: 600px;
}

.hero h1 {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero p {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    color: rgba(255, 255, 255, 0.9);
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-image {
    flex: 1;
    text-align: center;
    font-size: 15rem;
    color: rgba(255, 255, 255, 0.1);
}

/* ===== FEATURES SECTION ===== */
.features {
    padding: 80px 0;
    background: white;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid #f0f0f0;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

/* ===== LATEST AUCTIONS SECTION ===== */
.latest-auctions {
    padding: 80px 0;
    background: #f8f9fa;
}

.auctions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.auction-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.auction-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.auction-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.auction-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.auction-card:hover .auction-image img {
    transform: scale(1.05);
}

.auction-status {
    position: absolute;
    top: 15px;
    right: 15px;
}

.status-badge {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.active {
    background: #28a745;
    color: white;
}

.status-badge.ending-soon {
    background: #ffc107;
    color: #333;
}

.status-badge.completed {
    background: #6c757d;
    color: white;
}

.auction-timer {
    position: absolute;
    bottom: 15px;
    left: 15px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
}

.auction-timer.expired {
    background: rgba(220, 53, 69, 0.9);
}

.auction-info {
    padding: 1.5rem;
}

.auction-info h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.auction-details {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 14px;
    color: #666;
    flex-wrap: wrap;
}

.auction-details span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.auction-price {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.current-bid {
    display: flex;
    flex-direction: column;
}

.current-bid .label {
    font-size: 12px;
    color: #666;
    margin-bottom: 2px;
}

.current-bid .amount {
    font-size: 1.2rem;
    font-weight: 700;
    color: #007bff;
}

.bid-count {
    font-size: 14px;
    color: #666;
}

.auction-actions {
    display: flex;
    gap: 0.5rem;
}

.auction-actions .btn {
    flex: 1;
    padding: 10px;
    font-size: 14px;
}

/* ===== STATISTICS SECTION ===== */
.statistics {
    padding: 60px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* ===== FOOTER ===== */
.footer {
    background: #1a1a2e;
    color: white;
    padding: 40px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: white;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #ccc;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: white;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #333;
    color: #ccc;
}

/* ===== FORM STYLES ===== */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-control::placeholder {
    color: #999;
}

.textarea {
    resize: vertical;
    min-height: 120px;
}

.form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.half-width {
    flex: 1;
}

.error-message {
    color: #dc3545;
    font-size: 14px;
    margin-top: 5px;
    display: block;
}

.message-panel {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-weight: 500;
}

.message-panel.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message-panel.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.form-checkbox {
    margin-top: 3px;
}

.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #666;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #333;
}

/* ===== AUTH PAGES ===== */
.auth-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.auth-section {
    padding: 40px 0;
    min-height: calc(100vh - 200px);
    display: flex;
    align-items: center;
}

.auth-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.auth-card {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.register-card {
    grid-column: 1 / -1;
    max-width: 800px;
    margin: 0 auto;
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-header h2 {
    color: #333;
    margin-bottom: 0.5rem;
}

.auth-header p {
    color: #666;
}

.auth-divider {
    text-align: center;
    margin: 2rem 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e0e0e0;
}

.auth-divider span {
    background: white;
    padding: 0 1rem;
    color: #666;
}

.auth-link {
    color: #007bff;
    font-weight: 600;
}

.auth-link:hover {
    color: #0056b3;
}

.forgot-password {
    color: #007bff;
    font-size: 14px;
}

.auth-side {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 123, 255, 0.05));
    border-radius: 20px;
    padding: 3rem;
}

.auth-side-content {
    text-align: center;
}

.auth-side-content h3 {
    color: #333;
    margin-bottom: 1rem;
}

.auth-side-content p {
    color: #666;
    margin-bottom: 2rem;
}

.auth-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    text-align: right;
}

.feature-item i {
    color: #007bff;
    font-size: 1.2rem;
    width: 20px;
}

/* ===== PAGE HEADER ===== */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 0;
    text-align: center;
}

.page-header h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.page-header p {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
}

/* ===== FILTERS SECTION ===== */
.filters-section {
    padding: 40px 0;
    background: white;
}

.filters-card {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid #e0e0e0;
}

.filters-card h3 {
    margin-bottom: 1.5rem;
    color: #333;
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    align-items: end;
}

.filter-group label {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* ===== AUCTIONS SECTION ===== */
.auctions-section {
    padding: 40px 0;
}

.results-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.results-count {
    font-weight: 600;
    color: #333;
}

.sort-options {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sort-options label {
    font-weight: 600;
    color: #333;
}

.sort-options .form-control {
    width: auto;
    min-width: 150px;
}

.no-results {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.no-results-content i {
    font-size: 4rem;
    color: #ccc;
    margin-bottom: 1rem;
}

.no-results-content h3 {
    color: #333;
    margin-bottom: 1rem;
}

.no-results-content p {
    color: #666;
    margin-bottom: 2rem;
}

/* ===== PAGINATION ===== */
.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 3rem;
}

.pagination-container .btn {
    margin: 0 5px;
}

/* ===== ADD CAR FORM ===== */
.add-car-section {
    padding: 40px 0;
}

.add-car-card {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    max-width: 1000px;
    margin: 0 auto;
}

.progress-steps {
    display: flex;
    justify-content: center;
    margin-bottom: 3rem;
    position: relative;
}

.progress-steps::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 25%;
    right: 25%;
    height: 2px;
    background: #e0e0e0;
    z-index: 1;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    flex: 1;
    max-width: 200px;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e0e0e0;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.step.active .step-number {
    background: #007bff;
    color: white;
}

.step-title {
    font-size: 14px;
    font-weight: 600;
    color: #666;
    text-align: center;
}

.step.active .step-title {
    color: #007bff;
}

.form-step {
    display: none;
}

.form-step.active {
    display: block;
}

.form-step h3 {
    margin-bottom: 2rem;
    color: #333;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f0f0f0;
}

.step-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e0e0e0;
}

.file-upload-area {
    border: 2px dashed #007bff;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    background: #f8f9ff;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.file-upload-area:hover {
    border-color: #0056b3;
    background: #f0f4ff;
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.file-upload-text i {
    font-size: 3rem;
    color: #007bff;
    margin-bottom: 1rem;
}

.file-upload-text p {
    font-size: 1.1rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.file-upload-text small {
    color: #666;
}

.features-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.features-list input[type="checkbox"] {
    margin-left: 8px;
}

.form-text {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background: #1a1a2e;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hero {
        flex-direction: column;
        text-align: center;
        padding: 60px 0;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero-image {
        font-size: 8rem;
        margin-top: 2rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .auctions-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .auth-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .auth-side {
        order: -1;
    }

    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .filters-grid {
        grid-template-columns: 1fr;
    }

    .results-info {
        flex-direction: column;
        align-items: stretch;
    }

    .auction-details {
        flex-direction: column;
        gap: 0.5rem;
    }

    .auction-actions {
        flex-direction: column;
    }

    .progress-steps {
        flex-direction: column;
        gap: 1rem;
    }

    .progress-steps::before {
        display: none;
    }

    .step-actions {
        flex-direction: column;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1.1rem;
    }

    .hero-buttons {
        flex-direction: column;
    }

    .section-title {
        font-size: 2rem;
    }

    .feature-card {
        padding: 1.5rem;
    }

    .auth-card {
        padding: 2rem;
    }

    .add-car-card {
        padding: 2rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .page-header h1 {
        font-size: 2rem;
    }
}

/* ===== UTILITY CLASSES ===== */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 3rem; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

.rounded { border-radius: 8px; }
.rounded-lg { border-radius: 15px; }
.rounded-full { border-radius: 50%; }

.shadow { box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); }
.shadow-lg { box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15); }

.bg-primary { background-color: #007bff; }
.bg-secondary { background-color: #6c757d; }
.bg-success { background-color: #28a745; }
.bg-danger { background-color: #dc3545; }
.bg-warning { background-color: #ffc107; }
.bg-info { background-color: #17a2b8; }
.bg-light { background-color: #f8f9fa; }
.bg-dark { background-color: #343a40; }
.bg-white { background-color: white; }

.text-primary { color: #007bff; }
.text-secondary { color: #6c757d; }
.text-success { color: #28a745; }
.text-danger { color: #dc3545; }
.text-warning { color: #ffc107; }
.text-info { color: #17a2b8; }
.text-light { color: #f8f9fa; }
.text-dark { color: #343a40; }
.text-white { color: white; }
.text-muted { color: #6c757d; }
