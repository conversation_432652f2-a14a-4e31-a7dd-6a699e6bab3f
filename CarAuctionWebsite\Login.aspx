<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Login.aspx.cs" Inherits="CarAuctionWebsite.Login" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ar" dir="rtl">
<head runat="server">
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>تسجيل الدخول - مزادات السيارات</title>
    <link href="css/style.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
</head>
<body class="auth-page">
    <form id="form1" runat="server">
        <!-- Header -->
        <header class="header">
            <nav class="navbar">
                <div class="nav-container">
                    <div class="nav-logo">
                        <a href="Default.aspx">
                            <h2><i class="fas fa-car"></i> مزادات السيارات</h2>
                        </a>
                    </div>
                    <ul class="nav-menu">
                        <li class="nav-item">
                            <a href="Default.aspx" class="nav-link">الرئيسية</a>
                        </li>
                        <li class="nav-item">
                            <a href="Auctions.aspx" class="nav-link">المزادات</a>
                        </li>
                        <li class="nav-item">
                            <a href="Register.aspx" class="nav-link">التسجيل</a>
                        </li>
                    </ul>
                </div>
            </nav>
        </header>

        <!-- Login Section -->
        <section class="auth-section">
            <div class="auth-container">
                <div class="auth-card">
                    <div class="auth-header">
                        <h2><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</h2>
                        <p>أدخل بياناتك للوصول إلى حسابك</p>
                    </div>

                    <div class="auth-form">
                        <!-- Error/Success Messages -->
                        <asp:Panel ID="pnlMessage" runat="server" Visible="false" CssClass="message-panel">
                            <asp:Label ID="lblMessage" runat="server"></asp:Label>
                        </asp:Panel>

                        <!-- Username Field -->
                        <div class="form-group">
                            <label for="txtUsername">
                                <i class="fas fa-user"></i> اسم المستخدم
                            </label>
                            <asp:TextBox ID="txtUsername" runat="server" CssClass="form-control" 
                                placeholder="أدخل اسم المستخدم" MaxLength="50"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvUsername" runat="server" 
                                ControlToValidate="txtUsername" 
                                ErrorMessage="اسم المستخدم مطلوب" 
                                CssClass="error-message" 
                                Display="Dynamic"></asp:RequiredFieldValidator>
                        </div>

                        <!-- Password Field -->
                        <div class="form-group">
                            <label for="txtPassword">
                                <i class="fas fa-lock"></i> كلمة المرور
                            </label>
                            <div class="password-input">
                                <asp:TextBox ID="txtPassword" runat="server" TextMode="Password" 
                                    CssClass="form-control" placeholder="أدخل كلمة المرور" MaxLength="100"></asp:TextBox>
                                <span class="password-toggle" onclick="togglePassword('txtPassword')">
                                    <i class="fas fa-eye"></i>
                                </span>
                            </div>
                            <asp:RequiredFieldValidator ID="rfvPassword" runat="server" 
                                ControlToValidate="txtPassword" 
                                ErrorMessage="كلمة المرور مطلوبة" 
                                CssClass="error-message" 
                                Display="Dynamic"></asp:RequiredFieldValidator>
                        </div>

                        <!-- Remember Me -->
                        <div class="form-group checkbox-group">
                            <asp:CheckBox ID="chkRememberMe" runat="server" CssClass="form-checkbox" />
                            <label for="chkRememberMe">تذكرني</label>
                        </div>

                        <!-- Login Button -->
                        <div class="form-group">
                            <asp:Button ID="btnLogin" runat="server" Text="تسجيل الدخول" 
                                CssClass="btn btn-primary btn-full" OnClick="btnLogin_Click" />
                        </div>

                        <!-- Forgot Password -->
                        <div class="form-group text-center">
                            <a href="ForgotPassword.aspx" class="forgot-password">نسيت كلمة المرور؟</a>
                        </div>

                        <!-- Divider -->
                        <div class="auth-divider">
                            <span>أو</span>
                        </div>

                        <!-- Register Link -->
                        <div class="form-group text-center">
                            <p>ليس لديك حساب؟ 
                                <a href="Register.aspx" class="auth-link">سجل الآن</a>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Side Image/Info -->
                <div class="auth-side">
                    <div class="auth-side-content">
                        <h3>مرحباً بك مرة أخرى!</h3>
                        <p>سجل دخولك للوصول إلى حسابك والمشاركة في المزادات الحصرية</p>
                        <div class="auth-features">
                            <div class="feature-item">
                                <i class="fas fa-gavel"></i>
                                <span>شارك في المزادات المباشرة</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-car"></i>
                                <span>أضف سياراتك للبيع</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-history"></i>
                                <span>تتبع تاريخ مزاداتك</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-bell"></i>
                                <span>احصل على إشعارات فورية</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="footer">
            <div class="container">
                <div class="footer-bottom">
                    <p>&copy; 2024 مزادات السيارات. جميع الحقوق محفوظة.</p>
                </div>
            </div>
        </footer>
    </form>

    <script src="js/main.js"></script>
    <script>
        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const icon = event.target;
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }
    </script>
</body>
</html>
