<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Default.aspx.cs" Inherits="CarAuctionWebsite.Default" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ar" dir="rtl">
<head runat="server">
    <meta charset="utf-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Content-Language" content="ar" />
    <title>موقع المزادات الإلكترونية للسيارات</title>
    <link href="css/style.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet" />
</head>
<body>
    <form id="form1" runat="server">
        <!-- Header -->
        <header class="header">
            <nav class="navbar">
                <div class="nav-container">
                    <div class="nav-logo">
                        <h2><i class="fas fa-car"></i> مزادات السيارات</h2>
                    </div>
                    <ul class="nav-menu">
                        <li class="nav-item">
                            <a href="Default.aspx" class="nav-link active">الرئيسية</a>
                        </li>
                        <li class="nav-item">
                            <a href="Auctions.aspx" class="nav-link">المزادات</a>
                        </li>
                        <li class="nav-item">
                            <a href="AddCar.aspx" class="nav-link">إضافة سيارة</a>
                        </li>
                        <li class="nav-item">
                            <asp:LinkButton ID="lnkLogin" runat="server" CssClass="nav-link" PostBackUrl="Login.aspx">تسجيل الدخول</asp:LinkButton>
                        </li>
                        <li class="nav-item">
                            <asp:LinkButton ID="lnkRegister" runat="server" CssClass="nav-link btn-register" PostBackUrl="Register.aspx">التسجيل</asp:LinkButton>
                        </li>
                        <li class="nav-item" id="userMenu" runat="server" visible="false">
                            <asp:Label ID="lblWelcome" runat="server" CssClass="nav-link"></asp:Label>
                            <asp:LinkButton ID="lnkLogout" runat="server" CssClass="nav-link" OnClick="lnkLogout_Click">تسجيل الخروج</asp:LinkButton>
                        </li>
                    </ul>
                    <div class="hamburger">
                        <span class="bar"></span>
                        <span class="bar"></span>
                        <span class="bar"></span>
                    </div>
                </div>
            </nav>
        </header>

        <!-- Hero Section -->
        <section class="hero">
            <div class="hero-content">
                <h1>أفضل موقع للمزادات الإلكترونية للسيارات</h1>
                <p>اكتشف مجموعة واسعة من السيارات المستعملة والجديدة في مزادات شفافة وآمنة</p>
                <div class="hero-buttons">
                    <a href="Auctions.aspx" class="btn btn-primary">تصفح المزادات</a>
                    <a href="Register.aspx" class="btn btn-secondary">انضم إلينا</a>
                </div>
            </div>
            <div class="hero-image">
                <i class="fas fa-car-side"></i>
            </div>
        </section>

        <!-- Features Section -->
        <section class="features">
            <div class="container">
                <h2 class="section-title">لماذا تختار موقعنا؟</h2>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3>أمان وثقة</h3>
                        <p>نظام آمن ومضمون مع تقييمات للبائعين وضمان الجودة</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <h3>أفضل الأسعار</h3>
                        <p>احصل على أفضل الصفقات بدون وسطاء وبأسعار تنافسية</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <h3>مزادات مباشرة</h3>
                        <p>شارك في المزادات المباشرة واحصل على السيارة التي تريدها</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <h3>معلومات شاملة</h3>
                        <p>تفاصيل كاملة عن كل سيارة مع الصور والمواصفات</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Latest Auctions Section -->
        <section class="latest-auctions">
            <div class="container">
                <h2 class="section-title">أحدث المزادات</h2>
                <div class="auctions-grid">
                    <asp:Repeater ID="rptLatestAuctions" runat="server">
                        <ItemTemplate>
                            <div class="auction-card">
                                <div class="auction-image">
                                    <img src='<%# Eval("ImageUrl") %>' alt='<%# Eval("CarModel") %>' />
                                    <div class="auction-status">
                                        <span class="status-badge <%# Eval("StatusClass") %>"><%# Eval("Status") %></span>
                                    </div>
                                </div>
                                <div class="auction-info">
                                    <h3><%# Eval("CarModel") %></h3>
                                    <p class="auction-details">
                                        <i class="fas fa-calendar"></i> <%# Eval("Year") %> |
                                        <i class="fas fa-road"></i> <%# Eval("Mileage") %> كم
                                    </p>
                                    <div class="auction-price">
                                        <span class="current-bid"><%# Eval("CurrentBid") %> ريال</span>
                                        <span class="time-left"><%# Eval("TimeLeft") %></span>
                                    </div>
                                    <a href='<%# "AuctionDetails.aspx?id=" + Eval("AuctionId") %>' class="btn btn-primary btn-small">عرض التفاصيل</a>
                                </div>
                            </div>
                        </ItemTemplate>
                    </asp:Repeater>
                </div>
                <div class="text-center">
                    <a href="Auctions.aspx" class="btn btn-outline">عرض جميع المزادات</a>
                </div>
            </div>
        </section>

        <!-- Statistics Section -->
        <section class="statistics">
            <div class="container">
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">
                            <asp:Label ID="lblTotalAuctions" runat="server" Text="0"></asp:Label>
                        </div>
                        <div class="stat-label">مزاد نشط</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">
                            <asp:Label ID="lblTotalUsers" runat="server" Text="0"></asp:Label>
                        </div>
                        <div class="stat-label">مستخدم مسجل</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">
                            <asp:Label ID="lblTotalCars" runat="server" Text="0"></asp:Label>
                        </div>
                        <div class="stat-label">سيارة معروضة</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">
                            <asp:Label ID="lblCompletedAuctions" runat="server" Text="0"></asp:Label>
                        </div>
                        <div class="stat-label">مزاد مكتمل</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="footer">
            <div class="container">
                <div class="footer-content">
                    <div class="footer-section">
                        <h3><i class="fas fa-car"></i> مزادات السيارات</h3>
                        <p>أفضل منصة للمزادات الإلكترونية للسيارات في المملكة العربية السعودية</p>
                    </div>
                    <div class="footer-section">
                        <h4>روابط سريعة</h4>
                        <ul>
                            <li><a href="Default.aspx">الرئيسية</a></li>
                            <li><a href="Auctions.aspx">المزادات</a></li>
                            <li><a href="AddCar.aspx">إضافة سيارة</a></li>
                            <li><a href="About.aspx">من نحن</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h4>تواصل معنا</h4>
                        <p><i class="fas fa-phone"></i> +966 50 123 4567</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-map-marker-alt"></i> الرياض، المملكة العربية السعودية</p>
                    </div>
                </div>
                <div class="footer-bottom">
                    <p>&copy; 2024 مزادات السيارات. جميع الحقوق محفوظة.</p>
                </div>
            </div>
        </footer>
    </form>
    
    <script src="js/main.js"></script>
</body>
</html>
