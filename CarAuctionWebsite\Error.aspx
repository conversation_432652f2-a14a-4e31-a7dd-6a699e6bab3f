<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Error.aspx.cs" Inherits="CarAuctionWebsite.Error" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ar" dir="rtl">
<head runat="server">
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>خطأ - مزادات السيارات</title>
    <link href="css/style.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
</head>
<body>
    <form id="form1" runat="server">
        <div class="error-page">
            <div class="container">
                <div class="error-content">
                    <div class="error-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h1>عذراً، حدث خطأ!</h1>
                    <p>نعتذر عن هذا الخطأ. يرجى المحاولة مرة أخرى أو العودة إلى الصفحة الرئيسية.</p>
                    <div class="error-actions">
                        <a href="Default.aspx" class="btn btn-primary">
                            <i class="fas fa-home"></i> العودة للرئيسية
                        </a>
                        <button type="button" onclick="history.back()" class="btn btn-outline">
                            <i class="fas fa-arrow-right"></i> الصفحة السابقة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
    
    <style>
        .error-page {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .error-content {
            text-align: center;
            max-width: 500px;
            padding: 2rem;
        }
        
        .error-icon {
            font-size: 5rem;
            color: #ffc107;
            margin-bottom: 2rem;
        }
        
        .error-content h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        .error-content p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .error-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
    </style>
</body>
</html>
