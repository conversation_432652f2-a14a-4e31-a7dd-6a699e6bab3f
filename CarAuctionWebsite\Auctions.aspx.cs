using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace CarAuctionWebsite
{
    public partial class Auctions : System.Web.UI.Page
    {
        private string connectionString = ConfigurationManager.ConnectionStrings["CarAuctionDB"].ConnectionString;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                CheckUserSession();
                LoadYearDropDown();
                LoadAuctions();
            }
        }

        private void CheckUserSession()
        {
            if (Session["UserId"] != null)
            {
                // User is logged in
                lnkLogin.Visible = false;
                lnkRegister.Visible = false;
                userMenu.Visible = true;
                
                string firstName = Session["FirstName"]?.ToString() ?? "المستخدم";
                lblWelcome.Text = $"مرحباً، {firstName}";
            }
            else
            {
                // User is not logged in
                lnkLogin.Visible = true;
                lnkRegister.Visible = true;
                userMenu.Visible = false;
            }
        }

        private void LoadYearDropDown()
        {
            ddlYear.Items.Clear();
            ddlYear.Items.Add(new ListItem("جميع السنوات", ""));
            
            int currentYear = DateTime.Now.Year;
            for (int year = currentYear; year >= 1990; year--)
            {
                ddlYear.Items.Add(new ListItem(year.ToString(), year.ToString()));
            }
        }

        private void LoadAuctions()
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    string query = BuildAuctionQuery();
                    
                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        AddFilterParameters(cmd);
                        
                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            List<object> auctions = new List<object>();
                            int count = 0;
                            
                            while (reader.Read())
                            {
                                count++;
                                DateTime endDateTime = Convert.ToDateTime(reader["EndDateTime"]);
                                TimeSpan timeLeft = endDateTime - DateTime.Now;
                                
                                string timeLeftText = FormatTimeLeft(timeLeft);
                                string statusClass = GetStatusClass(reader["Status"].ToString(), timeLeft);
                                bool canBid = Session["UserId"] != null && 
                                            reader["Status"].ToString() == "Active" && 
                                            timeLeft.TotalSeconds > 0;
                                
                                auctions.Add(new
                                {
                                    AuctionId = reader["AuctionId"],
                                    CarModel = reader["CarModel"],
                                    Year = reader["Year"],
                                    Mileage = FormatMileage(Convert.ToInt32(reader["Mileage"])),
                                    FuelType = GetFuelTypeArabic(reader["FuelType"].ToString()),
                                    CurrentBid = FormatCurrency(Convert.ToDecimal(reader["CurrentBid"])),
                                    TimeLeft = timeLeftText,
                                    Status = GetStatusText(reader["Status"].ToString(), timeLeft),
                                    StatusClass = statusClass,
                                    ImageUrl = reader["ImageUrl"] ?? "images/default-car.jpg",
                                    BidCount = reader["BidCount"],
                                    EndDateTime = endDateTime.ToString("yyyy-MM-ddTHH:mm:ss"),
                                    CanBid = canBid
                                });
                            }
                            
                            lblResultsCount.Text = $"تم العثور على {count} مزاد";
                            
                            if (auctions.Count > 0)
                            {
                                rptAuctions.DataSource = auctions;
                                rptAuctions.DataBind();
                                pnlNoResults.Visible = false;
                            }
                            else
                            {
                                rptAuctions.DataSource = null;
                                rptAuctions.DataBind();
                                pnlNoResults.Visible = true;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading auctions: {ex.Message}");
                
                lblResultsCount.Text = "حدث خطأ في تحميل المزادات";
                rptAuctions.DataSource = new List<object>();
                rptAuctions.DataBind();
                pnlNoResults.Visible = true;
            }
        }

        private string BuildAuctionQuery()
        {
            string baseQuery = @"
                SELECT 
                    a.AuctionId,
                    c.Brand + ' ' + c.Model + ' ' + CAST(c.Year AS VARCHAR) AS CarModel,
                    c.Year,
                    c.Mileage,
                    c.FuelType,
                    a.CurrentBid,
                    a.EndDateTime,
                    a.Status,
                    ISNULL(c.ImageUrl, 'images/default-car.jpg') AS ImageUrl,
                    (SELECT COUNT(*) FROM Bids b WHERE b.AuctionId = a.AuctionId) AS BidCount
                FROM Auctions a
                INNER JOIN Cars c ON a.CarId = c.CarId
                WHERE 1=1";

            // Add search filter
            if (!string.IsNullOrEmpty(txtSearch.Text.Trim()))
            {
                baseQuery += " AND (c.Brand LIKE @Search OR c.Model LIKE @Search OR c.Description LIKE @Search)";
            }

            // Add brand filter
            if (!string.IsNullOrEmpty(ddlBrand.SelectedValue))
            {
                baseQuery += " AND c.Brand = @Brand";
            }

            // Add year filter
            if (!string.IsNullOrEmpty(ddlYear.SelectedValue))
            {
                baseQuery += " AND c.Year = @Year";
            }

            // Add price range filter
            if (!string.IsNullOrEmpty(ddlPriceRange.SelectedValue))
            {
                string[] priceRange = ddlPriceRange.SelectedValue.Split('-');
                if (priceRange.Length == 2)
                {
                    baseQuery += " AND a.CurrentBid BETWEEN @MinPrice AND @MaxPrice";
                }
            }

            // Add status filter
            if (!string.IsNullOrEmpty(ddlStatus.SelectedValue))
            {
                if (ddlStatus.SelectedValue == "Ending Soon")
                {
                    baseQuery += " AND a.Status = 'Active' AND a.EndDateTime <= DATEADD(HOUR, 24, GETDATE())";
                }
                else
                {
                    baseQuery += " AND a.Status = @Status";
                }
            }

            // Add sorting
            string sortField = ddlSort.SelectedValue;
            switch (sortField)
            {
                case "CurrentBid":
                    baseQuery += " ORDER BY a.CurrentBid DESC";
                    break;
                case "Year":
                    baseQuery += " ORDER BY c.Year DESC";
                    break;
                case "Brand":
                    baseQuery += " ORDER BY c.Brand, c.Model";
                    break;
                default:
                    baseQuery += " ORDER BY a.EndDateTime ASC";
                    break;
            }

            return baseQuery;
        }

        private void AddFilterParameters(SqlCommand cmd)
        {
            if (!string.IsNullOrEmpty(txtSearch.Text.Trim()))
            {
                cmd.Parameters.AddWithValue("@Search", "%" + txtSearch.Text.Trim() + "%");
            }

            if (!string.IsNullOrEmpty(ddlBrand.SelectedValue))
            {
                cmd.Parameters.AddWithValue("@Brand", ddlBrand.SelectedValue);
            }

            if (!string.IsNullOrEmpty(ddlYear.SelectedValue))
            {
                cmd.Parameters.AddWithValue("@Year", ddlYear.SelectedValue);
            }

            if (!string.IsNullOrEmpty(ddlPriceRange.SelectedValue))
            {
                string[] priceRange = ddlPriceRange.SelectedValue.Split('-');
                if (priceRange.Length == 2)
                {
                    cmd.Parameters.AddWithValue("@MinPrice", decimal.Parse(priceRange[0]));
                    cmd.Parameters.AddWithValue("@MaxPrice", decimal.Parse(priceRange[1]));
                }
            }

            if (!string.IsNullOrEmpty(ddlStatus.SelectedValue) && ddlStatus.SelectedValue != "Ending Soon")
            {
                cmd.Parameters.AddWithValue("@Status", ddlStatus.SelectedValue);
            }
        }

        protected void ApplyFilters(object sender, EventArgs e)
        {
            LoadAuctions();
        }

        protected void btnClearFilters_Click(object sender, EventArgs e)
        {
            txtSearch.Text = "";
            ddlBrand.SelectedIndex = 0;
            ddlYear.SelectedIndex = 0;
            ddlPriceRange.SelectedIndex = 0;
            ddlStatus.SelectedIndex = 0;
            ddlSort.SelectedIndex = 0;
            
            LoadAuctions();
        }

        protected void btnPlaceBid_Click(object sender, EventArgs e)
        {
            if (Session["UserId"] == null)
            {
                Response.Redirect("Login.aspx?ReturnUrl=" + Request.Url.PathAndQuery);
                return;
            }

            LinkButton btn = (LinkButton)sender;
            int auctionId = Convert.ToInt32(btn.CommandArgument);
            
            // Redirect to auction details page for bidding
            Response.Redirect($"AuctionDetails.aspx?id={auctionId}");
        }

        protected void lnkLogout_Click(object sender, EventArgs e)
        {
            Session.Clear();
            Session.Abandon();
            Response.Redirect("Default.aspx");
        }

        private string FormatTimeLeft(TimeSpan timeLeft)
        {
            if (timeLeft.TotalDays >= 1)
            {
                return $"{(int)timeLeft.TotalDays} يوم {timeLeft.Hours} ساعة";
            }
            else if (timeLeft.TotalHours >= 1)
            {
                return $"{timeLeft.Hours} ساعة {timeLeft.Minutes} دقيقة";
            }
            else if (timeLeft.TotalMinutes >= 1)
            {
                return $"{timeLeft.Minutes} دقيقة";
            }
            else if (timeLeft.TotalSeconds > 0)
            {
                return "أقل من دقيقة";
            }
            else
            {
                return "انتهى";
            }
        }

        private string GetStatusClass(string status, TimeSpan timeLeft)
        {
            if (status != "Active")
                return "completed";
            
            if (timeLeft.TotalHours <= 24)
                return "ending-soon";
            
            return "active";
        }

        private string GetStatusText(string status, TimeSpan timeLeft)
        {
            if (status != "Active")
                return "مكتمل";
            
            if (timeLeft.TotalSeconds <= 0)
                return "انتهى";
            
            if (timeLeft.TotalHours <= 24)
                return "ينتهي قريباً";
            
            return "نشط";
        }

        private string GetFuelTypeArabic(string fuelType)
        {
            switch (fuelType.ToLower())
            {
                case "gasoline": return "بنزين";
                case "diesel": return "ديزل";
                case "hybrid": return "هجين";
                case "electric": return "كهربائي";
                default: return fuelType;
            }
        }

        private string FormatCurrency(decimal amount)
        {
            return amount.ToString("N0") + " ريال";
        }

        private string FormatMileage(int mileage)
        {
            return mileage.ToString("N0");
        }
    }
}
