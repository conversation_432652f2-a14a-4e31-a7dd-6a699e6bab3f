using System;

namespace CarAuctionWebsite
{
    public partial class Error : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // Log the error details if available
            if (Session["LastError"] != null)
            {
                Exception ex = (Exception)Session["LastError"];
                System.Diagnostics.Debug.WriteLine($"Error Page - Exception: {ex.Message}");
                
                // Clear the error from session
                Session.Remove("LastError");
            }
        }
    }
}
