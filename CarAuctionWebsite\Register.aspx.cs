using System;
using System.Configuration;
using System.Data.SqlClient;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Web.UI;

namespace CarAuctionWebsite
{
    public partial class Register : System.Web.UI.Page
    {
        // تم إزالة connectionString مؤقتاً

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // Check if user is already logged in
                if (Session["UserId"] != null)
                {
                    Response.Redirect("Default.aspx");
                }
                
                // Focus on first name field
                txtFirstName.Focus();
            }
        }

        protected void btnRegister_Click(object sender, EventArgs e)
        {
            if (Page.IsValid && ValidateCustomFields())
            {
                if (CreateUser())
                {
                    ShowMessage("تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول.", "success");
                    
                    // Redirect to login page after 2 seconds
                    Response.AddHeader("REFRESH", "2;URL=Login.aspx");
                }
            }
        }

        private bool ValidateCustomFields()
        {
            bool isValid = true;
            
            // Check if username already exists
            if (IsUsernameExists(txtUsername.Text.Trim()))
            {
                ShowMessage("اسم المستخدم موجود بالفعل. يرجى اختيار اسم مستخدم آخر.", "error");
                isValid = false;
            }
            
            // Check if email already exists
            if (IsEmailExists(txtEmail.Text.Trim()))
            {
                ShowMessage("البريد الإلكتروني مسجل بالفعل. يرجى استخدام بريد إلكتروني آخر.", "error");
                isValid = false;
            }
            
            // Validate phone number format
            if (!IsValidPhoneNumber(txtPhone.Text.Trim()))
            {
                ShowMessage("رقم الهاتف غير صحيح. يجب أن يبدأ بـ 05 ويحتوي على 10 أرقام.", "error");
                isValid = false;
            }
            
            return isValid;
        }

        private bool IsUsernameExists(string username)
        {
            try
            {
                // مؤقتاً: قائمة المستخدمين الموجودين
                var existingUsers = new[] { "ahmed123", "fatima456", "mohammed789", "admin" };
                return existingUsers.Contains(username.ToLower());
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking username: {ex.Message}");
                return false;
            }
        }

        private bool IsEmailExists(string email)
        {
            try
            {
                // مؤقتاً: قائمة الإيميلات الموجودة
                var existingEmails = new[] { "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>" };
                return existingEmails.Contains(email.ToLower());
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking email: {ex.Message}");
                return false;
            }
        }

        private bool IsValidPhoneNumber(string phone)
        {
            // Saudi phone number format: 05xxxxxxxx or 5xxxxxxxx
            Regex phoneRegex = new Regex(@"^(05|5)[0-9]{8}$");
            return phoneRegex.IsMatch(phone);
        }

        private bool CreateUser()
        {
            try
            {
                // مؤقتاً: حفظ بيانات المستخدم في السجل
                string userInfo = $"New User: {txtFirstName.Text} {txtLastName.Text}, Username: {txtUsername.Text}, Email: {txtEmail.Text}, Phone: {txtPhone.Text}";
                System.Diagnostics.Debug.WriteLine($"User registered: {userInfo}");

                // في التطبيق الحقيقي، ستحفظ البيانات في قاعدة البيانات
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating user: {ex.Message}");
                ShowMessage("حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى.", "error");
                return false;
            }
        }

        private string HashPassword(string password)
        {
            using (SHA256 sha256Hash = SHA256.Create())
            {
                byte[] bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(password + "CarAuctionSalt"));
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < bytes.Length; i++)
                {
                    builder.Append(bytes[i].ToString("x2"));
                }
                return builder.ToString();
            }
        }

        private void ShowMessage(string message, string type)
        {
            lblMessage.Text = message;
            pnlMessage.CssClass = $"message-panel {type}";
            pnlMessage.Visible = true;
        }
    }
}
