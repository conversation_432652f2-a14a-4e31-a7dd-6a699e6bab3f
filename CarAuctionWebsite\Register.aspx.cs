using System;
using System.Configuration;
using System.Data.SqlClient;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Web.UI;

namespace CarAuctionWebsite
{
    public partial class Register : System.Web.UI.Page
    {
        private string connectionString = ConfigurationManager.ConnectionStrings["CarAuctionDB"].ConnectionString;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // Check if user is already logged in
                if (Session["UserId"] != null)
                {
                    Response.Redirect("Default.aspx");
                }
                
                // Focus on first name field
                txtFirstName.Focus();
            }
        }

        protected void btnRegister_Click(object sender, EventArgs e)
        {
            if (Page.IsValid && ValidateCustomFields())
            {
                if (CreateUser())
                {
                    ShowMessage("تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول.", "success");
                    
                    // Redirect to login page after 2 seconds
                    Response.AddHeader("REFRESH", "2;URL=Login.aspx");
                }
            }
        }

        private bool ValidateCustomFields()
        {
            bool isValid = true;
            
            // Check if username already exists
            if (IsUsernameExists(txtUsername.Text.Trim()))
            {
                ShowMessage("اسم المستخدم موجود بالفعل. يرجى اختيار اسم مستخدم آخر.", "error");
                isValid = false;
            }
            
            // Check if email already exists
            if (IsEmailExists(txtEmail.Text.Trim()))
            {
                ShowMessage("البريد الإلكتروني مسجل بالفعل. يرجى استخدام بريد إلكتروني آخر.", "error");
                isValid = false;
            }
            
            // Validate phone number format
            if (!IsValidPhoneNumber(txtPhone.Text.Trim()))
            {
                ShowMessage("رقم الهاتف غير صحيح. يجب أن يبدأ بـ 05 ويحتوي على 10 أرقام.", "error");
                isValid = false;
            }
            
            return isValid;
        }

        private bool IsUsernameExists(string username)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    string query = "SELECT COUNT(*) FROM Users WHERE Username = @Username";
                    
                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@Username", username);
                        
                        conn.Open();
                        int count = (int)cmd.ExecuteScalar();
                        return count > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking username: {ex.Message}");
                return true; // Assume exists to be safe
            }
        }

        private bool IsEmailExists(string email)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    string query = "SELECT COUNT(*) FROM Users WHERE Email = @Email";
                    
                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@Email", email);
                        
                        conn.Open();
                        int count = (int)cmd.ExecuteScalar();
                        return count > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking email: {ex.Message}");
                return true; // Assume exists to be safe
            }
        }

        private bool IsValidPhoneNumber(string phone)
        {
            // Saudi phone number format: 05xxxxxxxx or 5xxxxxxxx
            Regex phoneRegex = new Regex(@"^(05|5)[0-9]{8}$");
            return phoneRegex.IsMatch(phone);
        }

        private bool CreateUser()
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    string query = @"
                        INSERT INTO Users (
                            FirstName, LastName, Username, Email, Phone, 
                            PasswordHash, CreatedDate, IsActive
                        ) VALUES (
                            @FirstName, @LastName, @Username, @Email, @Phone, 
                            @PasswordHash, GETDATE(), 1
                        )";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@FirstName", txtFirstName.Text.Trim());
                        cmd.Parameters.AddWithValue("@LastName", txtLastName.Text.Trim());
                        cmd.Parameters.AddWithValue("@Username", txtUsername.Text.Trim());
                        cmd.Parameters.AddWithValue("@Email", txtEmail.Text.Trim());
                        cmd.Parameters.AddWithValue("@Phone", txtPhone.Text.Trim());
                        cmd.Parameters.AddWithValue("@PasswordHash", HashPassword(txtPassword.Text));
                        
                        conn.Open();
                        int result = cmd.ExecuteNonQuery();
                        
                        return result > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating user: {ex.Message}");
                ShowMessage("حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى.", "error");
                return false;
            }
        }

        private string HashPassword(string password)
        {
            using (SHA256 sha256Hash = SHA256.Create())
            {
                byte[] bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(password + "CarAuctionSalt"));
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < bytes.Length; i++)
                {
                    builder.Append(bytes[i].ToString("x2"));
                }
                return builder.ToString();
            }
        }

        private void ShowMessage(string message, string type)
        {
            lblMessage.Text = message;
            pnlMessage.CssClass = $"message-panel {type}";
            pnlMessage.Visible = true;
        }
    }
}
