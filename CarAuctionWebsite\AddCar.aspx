<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="AddCar.aspx.cs" Inherits="CarAuctionWebsite.AddCar" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ar" dir="rtl">
<head runat="server">
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>إضافة سيارة - مزادات السيارات</title>
    <link href="css/style.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
</head>
<body>
    <form id="form1" runat="server" enctype="multipart/form-data">
        <!-- Header -->
        <header class="header">
            <nav class="navbar">
                <div class="nav-container">
                    <div class="nav-logo">
                        <a href="Default.aspx">
                            <h2><i class="fas fa-car"></i> مزادات السيارات</h2>
                        </a>
                    </div>
                    <ul class="nav-menu">
                        <li class="nav-item">
                            <a href="Default.aspx" class="nav-link">الرئيسية</a>
                        </li>
                        <li class="nav-item">
                            <a href="Auctions.aspx" class="nav-link">المزادات</a>
                        </li>
                        <li class="nav-item">
                            <a href="AddCar.aspx" class="nav-link active">إضافة سيارة</a>
                        </li>
                        <li class="nav-item">
                            <asp:LinkButton ID="lnkLogin" runat="server" CssClass="nav-link" PostBackUrl="Login.aspx">تسجيل الدخول</asp:LinkButton>
                        </li>
                        <li class="nav-item">
                            <asp:LinkButton ID="lnkRegister" runat="server" CssClass="nav-link btn-register" PostBackUrl="Register.aspx">التسجيل</asp:LinkButton>
                        </li>
                        <li class="nav-item" id="userMenu" runat="server" visible="false">
                            <asp:Label ID="lblWelcome" runat="server" CssClass="nav-link"></asp:Label>
                            <asp:LinkButton ID="lnkLogout" runat="server" CssClass="nav-link" OnClick="lnkLogout_Click">تسجيل الخروج</asp:LinkButton>
                        </li>
                    </ul>
                    <div class="hamburger">
                        <span class="bar"></span>
                        <span class="bar"></span>
                        <span class="bar"></span>
                    </div>
                </div>
            </nav>
        </header>

        <!-- Page Header -->
        <section class="page-header">
            <div class="container">
                <h1><i class="fas fa-plus-circle"></i> إضافة سيارة للمزاد</h1>
                <p>أضف سيارتك للمزاد واحصل على أفضل سعر</p>
            </div>
        </section>

        <!-- Add Car Form -->
        <section class="add-car-section">
            <div class="container">
                <div class="add-car-card">
                    <!-- Progress Steps -->
                    <div class="progress-steps">
                        <div class="step active" data-step="1">
                            <div class="step-number">1</div>
                            <div class="step-title">معلومات السيارة</div>
                        </div>
                        <div class="step" data-step="2">
                            <div class="step-number">2</div>
                            <div class="step-title">الصور والوصف</div>
                        </div>
                        <div class="step" data-step="3">
                            <div class="step-number">3</div>
                            <div class="step-title">تفاصيل المزاد</div>
                        </div>
                    </div>

                    <!-- Error/Success Messages -->
                    <asp:Panel ID="pnlMessage" runat="server" Visible="false" CssClass="message-panel">
                        <asp:Label ID="lblMessage" runat="server"></asp:Label>
                    </asp:Panel>

                    <!-- Step 1: Car Information -->
                    <div class="form-step active" id="step1">
                        <h3><i class="fas fa-car"></i> معلومات السيارة الأساسية</h3>
                        
                        <div class="form-row">
                            <!-- Brand -->
                            <div class="form-group half-width">
                                <label for="ddlBrand">
                                    <i class="fas fa-tag"></i> الماركة *
                                </label>
                                <asp:DropDownList ID="ddlBrand" runat="server" CssClass="form-control" AutoPostBack="true" OnSelectedIndexChanged="ddlBrand_SelectedIndexChanged">
                                    <asp:ListItem Value="">اختر الماركة</asp:ListItem>
                                    <asp:ListItem Value="Toyota">تويوتا</asp:ListItem>
                                    <asp:ListItem Value="Honda">هوندا</asp:ListItem>
                                    <asp:ListItem Value="Nissan">نيسان</asp:ListItem>
                                    <asp:ListItem Value="Hyundai">هيونداي</asp:ListItem>
                                    <asp:ListItem Value="BMW">بي إم دبليو</asp:ListItem>
                                    <asp:ListItem Value="Mercedes">مرسيدس</asp:ListItem>
                                    <asp:ListItem Value="Audi">أودي</asp:ListItem>
                                    <asp:ListItem Value="Lexus">لكزس</asp:ListItem>
                                    <asp:ListItem Value="Ford">فورد</asp:ListItem>
                                    <asp:ListItem Value="Chevrolet">شيفروليه</asp:ListItem>
                                </asp:DropDownList>
                                <asp:RequiredFieldValidator ID="rfvBrand" runat="server" 
                                    ControlToValidate="ddlBrand" ErrorMessage="الماركة مطلوبة" 
                                    CssClass="error-message" Display="Dynamic" ValidationGroup="Step1"></asp:RequiredFieldValidator>
                            </div>

                            <!-- Model -->
                            <div class="form-group half-width">
                                <label for="ddlModel">
                                    <i class="fas fa-car-side"></i> الموديل *
                                </label>
                                <asp:DropDownList ID="ddlModel" runat="server" CssClass="form-control">
                                    <asp:ListItem Value="">اختر الموديل</asp:ListItem>
                                </asp:DropDownList>
                                <asp:RequiredFieldValidator ID="rfvModel" runat="server" 
                                    ControlToValidate="ddlModel" ErrorMessage="الموديل مطلوب" 
                                    CssClass="error-message" Display="Dynamic" ValidationGroup="Step1"></asp:RequiredFieldValidator>
                            </div>
                        </div>

                        <div class="form-row">
                            <!-- Year -->
                            <div class="form-group half-width">
                                <label for="ddlYear">
                                    <i class="fas fa-calendar"></i> سنة الصنع *
                                </label>
                                <asp:DropDownList ID="ddlYear" runat="server" CssClass="form-control">
                                    <asp:ListItem Value="">اختر السنة</asp:ListItem>
                                </asp:DropDownList>
                                <asp:RequiredFieldValidator ID="rfvYear" runat="server" 
                                    ControlToValidate="ddlYear" ErrorMessage="سنة الصنع مطلوبة" 
                                    CssClass="error-message" Display="Dynamic" ValidationGroup="Step1"></asp:RequiredFieldValidator>
                            </div>

                            <!-- Mileage -->
                            <div class="form-group half-width">
                                <label for="txtMileage">
                                    <i class="fas fa-road"></i> المسافة المقطوعة (كم) *
                                </label>
                                <asp:TextBox ID="txtMileage" runat="server" CssClass="form-control" 
                                    placeholder="مثال: 50000" TextMode="Number"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvMileage" runat="server" 
                                    ControlToValidate="txtMileage" ErrorMessage="المسافة المقطوعة مطلوبة" 
                                    CssClass="error-message" Display="Dynamic" ValidationGroup="Step1"></asp:RequiredFieldValidator>
                                <asp:RangeValidator ID="rvMileage" runat="server" 
                                    ControlToValidate="txtMileage" MinimumValue="0" MaximumValue="999999" 
                                    Type="Integer" ErrorMessage="المسافة المقطوعة غير صحيحة" 
                                    CssClass="error-message" Display="Dynamic" ValidationGroup="Step1"></asp:RangeValidator>
                            </div>
                        </div>

                        <div class="form-row">
                            <!-- Fuel Type -->
                            <div class="form-group half-width">
                                <label for="ddlFuelType">
                                    <i class="fas fa-gas-pump"></i> نوع الوقود *
                                </label>
                                <asp:DropDownList ID="ddlFuelType" runat="server" CssClass="form-control">
                                    <asp:ListItem Value="">اختر نوع الوقود</asp:ListItem>
                                    <asp:ListItem Value="Gasoline">بنزين</asp:ListItem>
                                    <asp:ListItem Value="Diesel">ديزل</asp:ListItem>
                                    <asp:ListItem Value="Hybrid">هجين</asp:ListItem>
                                    <asp:ListItem Value="Electric">كهربائي</asp:ListItem>
                                </asp:DropDownList>
                                <asp:RequiredFieldValidator ID="rfvFuelType" runat="server" 
                                    ControlToValidate="ddlFuelType" ErrorMessage="نوع الوقود مطلوب" 
                                    CssClass="error-message" Display="Dynamic" ValidationGroup="Step1"></asp:RequiredFieldValidator>
                            </div>

                            <!-- Transmission -->
                            <div class="form-group half-width">
                                <label for="ddlTransmission">
                                    <i class="fas fa-cogs"></i> ناقل الحركة *
                                </label>
                                <asp:DropDownList ID="ddlTransmission" runat="server" CssClass="form-control">
                                    <asp:ListItem Value="">اختر ناقل الحركة</asp:ListItem>
                                    <asp:ListItem Value="Manual">يدوي</asp:ListItem>
                                    <asp:ListItem Value="Automatic">أوتوماتيك</asp:ListItem>
                                    <asp:ListItem Value="CVT">CVT</asp:ListItem>
                                </asp:DropDownList>
                                <asp:RequiredFieldValidator ID="rfvTransmission" runat="server" 
                                    ControlToValidate="ddlTransmission" ErrorMessage="ناقل الحركة مطلوب" 
                                    CssClass="error-message" Display="Dynamic" ValidationGroup="Step1"></asp:RequiredFieldValidator>
                            </div>
                        </div>

                        <div class="form-row">
                            <!-- Color -->
                            <div class="form-group half-width">
                                <label for="ddlColor">
                                    <i class="fas fa-palette"></i> اللون *
                                </label>
                                <asp:DropDownList ID="ddlColor" runat="server" CssClass="form-control">
                                    <asp:ListItem Value="">اختر اللون</asp:ListItem>
                                    <asp:ListItem Value="White">أبيض</asp:ListItem>
                                    <asp:ListItem Value="Black">أسود</asp:ListItem>
                                    <asp:ListItem Value="Silver">فضي</asp:ListItem>
                                    <asp:ListItem Value="Gray">رمادي</asp:ListItem>
                                    <asp:ListItem Value="Red">أحمر</asp:ListItem>
                                    <asp:ListItem Value="Blue">أزرق</asp:ListItem>
                                    <asp:ListItem Value="Green">أخضر</asp:ListItem>
                                    <asp:ListItem Value="Brown">بني</asp:ListItem>
                                    <asp:ListItem Value="Gold">ذهبي</asp:ListItem>
                                </asp:DropDownList>
                                <asp:RequiredFieldValidator ID="rfvColor" runat="server" 
                                    ControlToValidate="ddlColor" ErrorMessage="اللون مطلوب" 
                                    CssClass="error-message" Display="Dynamic" ValidationGroup="Step1"></asp:RequiredFieldValidator>
                            </div>

                            <!-- Engine Size -->
                            <div class="form-group half-width">
                                <label for="txtEngineSize">
                                    <i class="fas fa-engine"></i> حجم المحرك (لتر)
                                </label>
                                <asp:TextBox ID="txtEngineSize" runat="server" CssClass="form-control" 
                                    placeholder="مثال: 2.0" TextMode="Number" step="0.1"></asp:TextBox>
                            </div>
                        </div>

                        <!-- Condition -->
                        <div class="form-group">
                            <label for="ddlCondition">
                                <i class="fas fa-star"></i> حالة السيارة *
                            </label>
                            <asp:DropDownList ID="ddlCondition" runat="server" CssClass="form-control">
                                <asp:ListItem Value="">اختر حالة السيارة</asp:ListItem>
                                <asp:ListItem Value="Excellent">ممتازة</asp:ListItem>
                                <asp:ListItem Value="Very Good">جيدة جداً</asp:ListItem>
                                <asp:ListItem Value="Good">جيدة</asp:ListItem>
                                <asp:ListItem Value="Fair">مقبولة</asp:ListItem>
                                <asp:ListItem Value="Poor">تحتاج إصلاح</asp:ListItem>
                            </asp:DropDownList>
                            <asp:RequiredFieldValidator ID="rfvCondition" runat="server" 
                                ControlToValidate="ddlCondition" ErrorMessage="حالة السيارة مطلوبة" 
                                CssClass="error-message" Display="Dynamic" ValidationGroup="Step1"></asp:RequiredFieldValidator>
                        </div>

                        <div class="step-actions">
                            <asp:Button ID="btnNext1" runat="server" Text="التالي" 
                                CssClass="btn btn-primary" OnClientClick="return nextStep(1);" 
                                ValidationGroup="Step1" />
                        </div>
                    </div>

                    <!-- Step 2: Images and Description -->
                    <div class="form-step" id="step2">
                        <h3><i class="fas fa-images"></i> الصور والوصف</h3>
                        
                        <!-- Car Images -->
                        <div class="form-group">
                            <label for="fuCarImages">
                                <i class="fas fa-camera"></i> صور السيارة *
                            </label>
                            <div class="file-upload-area">
                                <asp:FileUpload ID="fuCarImages" runat="server"
                                    CssClass="file-input" accept="image/*" />
                                <div class="file-upload-text">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <p>اسحب الصورة هنا أو انقر للاختيار</p>
                                    <small>رفع صورة واحدة (JPG, PNG, GIF - حد أقصى 5MB)</small>
                                </div>
                            </div>
                            <asp:RequiredFieldValidator ID="rfvImages" runat="server" 
                                ControlToValidate="fuCarImages" ErrorMessage="صور السيارة مطلوبة" 
                                CssClass="error-message" Display="Dynamic" ValidationGroup="Step2"></asp:RequiredFieldValidator>
                        </div>

                        <!-- Description -->
                        <div class="form-group">
                            <label for="txtDescription">
                                <i class="fas fa-align-left"></i> وصف السيارة *
                            </label>
                            <asp:TextBox ID="txtDescription" runat="server" TextMode="MultiLine" 
                                CssClass="form-control textarea" Rows="6" 
                                placeholder="اكتب وصفاً مفصلاً عن السيارة، حالتها، المميزات، التاريخ، أي أضرار أو إصلاحات..."></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvDescription" runat="server" 
                                ControlToValidate="txtDescription" ErrorMessage="وصف السيارة مطلوب" 
                                CssClass="error-message" Display="Dynamic" ValidationGroup="Step2"></asp:RequiredFieldValidator>
                        </div>

                        <!-- Additional Features -->
                        <div class="form-group">
                            <label>
                                <i class="fas fa-plus"></i> المميزات الإضافية
                            </label>
                            <div class="features-grid">
                                <asp:CheckBoxList ID="cblFeatures" runat="server" CssClass="features-list">
                                    <asp:ListItem Value="AirConditioning">تكييف</asp:ListItem>
                                    <asp:ListItem Value="PowerSteering">مقود كهربائي</asp:ListItem>
                                    <asp:ListItem Value="PowerWindows">نوافذ كهربائية</asp:ListItem>
                                    <asp:ListItem Value="CentralLocking">قفل مركزي</asp:ListItem>
                                    <asp:ListItem Value="ABS">نظام ABS</asp:ListItem>
                                    <asp:ListItem Value="Airbags">وسائد هوائية</asp:ListItem>
                                    <asp:ListItem Value="GPS">نظام GPS</asp:ListItem>
                                    <asp:ListItem Value="Bluetooth">بلوتوث</asp:ListItem>
                                    <asp:ListItem Value="BackupCamera">كاميرا خلفية</asp:ListItem>
                                    <asp:ListItem Value="SunRoof">فتحة سقف</asp:ListItem>
                                    <asp:ListItem Value="LeatherSeats">مقاعد جلدية</asp:ListItem>
                                    <asp:ListItem Value="CruiseControl">مثبت سرعة</asp:ListItem>
                                </asp:CheckBoxList>
                            </div>
                        </div>

                        <div class="step-actions">
                            <button type="button" class="btn btn-outline" onclick="prevStep(2)">السابق</button>
                            <asp:Button ID="btnNext2" runat="server" Text="التالي" 
                                CssClass="btn btn-primary" OnClientClick="return nextStep(2);" 
                                ValidationGroup="Step2" />
                        </div>
                    </div>

                    <!-- Step 3: Auction Details -->
                    <div class="form-step" id="step3">
                        <h3><i class="fas fa-gavel"></i> تفاصيل المزاد</h3>
                        
                        <div class="form-row">
                            <!-- Starting Price -->
                            <div class="form-group half-width">
                                <label for="txtStartingPrice">
                                    <i class="fas fa-dollar-sign"></i> السعر الابتدائي (ريال) *
                                </label>
                                <asp:TextBox ID="txtStartingPrice" runat="server" CssClass="form-control" 
                                    placeholder="مثال: 50000" TextMode="Number"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvStartingPrice" runat="server" 
                                    ControlToValidate="txtStartingPrice" ErrorMessage="السعر الابتدائي مطلوب" 
                                    CssClass="error-message" Display="Dynamic" ValidationGroup="Step3"></asp:RequiredFieldValidator>
                                <asp:RangeValidator ID="rvStartingPrice" runat="server" 
                                    ControlToValidate="txtStartingPrice" MinimumValue="1000" MaximumValue="10000000" 
                                    Type="Integer" ErrorMessage="السعر يجب أن يكون بين 1,000 و 10,000,000 ريال" 
                                    CssClass="error-message" Display="Dynamic" ValidationGroup="Step3"></asp:RangeValidator>
                            </div>

                            <!-- Reserve Price -->
                            <div class="form-group half-width">
                                <label for="txtReservePrice">
                                    <i class="fas fa-shield-alt"></i> السعر المحجوز (ريال)
                                </label>
                                <asp:TextBox ID="txtReservePrice" runat="server" CssClass="form-control" 
                                    placeholder="اختياري - أقل سعر مقبول" TextMode="Number"></asp:TextBox>
                                <small class="form-text">السعر المحجوز هو أقل سعر تقبل بيع السيارة به (اختياري)</small>
                            </div>
                        </div>

                        <!-- Auction Duration -->
                        <div class="form-group">
                            <label for="ddlDuration">
                                <i class="fas fa-clock"></i> مدة المزاد *
                            </label>
                            <asp:DropDownList ID="ddlDuration" runat="server" CssClass="form-control">
                                <asp:ListItem Value="">اختر مدة المزاد</asp:ListItem>
                                <asp:ListItem Value="3">3 أيام</asp:ListItem>
                                <asp:ListItem Value="5">5 أيام</asp:ListItem>
                                <asp:ListItem Value="7">7 أيام</asp:ListItem>
                                <asp:ListItem Value="10">10 أيام</asp:ListItem>
                                <asp:ListItem Value="14">14 يوم</asp:ListItem>
                            </asp:DropDownList>
                            <asp:RequiredFieldValidator ID="rfvDuration" runat="server" 
                                ControlToValidate="ddlDuration" ErrorMessage="مدة المزاد مطلوبة" 
                                CssClass="error-message" Display="Dynamic" ValidationGroup="Step3"></asp:RequiredFieldValidator>
                        </div>

                        <!-- Contact Information -->
                        <div class="form-row">
                            <div class="form-group half-width">
                                <label for="txtContactPhone">
                                    <i class="fas fa-phone"></i> رقم الهاتف للتواصل *
                                </label>
                                <asp:TextBox ID="txtContactPhone" runat="server" CssClass="form-control" 
                                    placeholder="05xxxxxxxx"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvContactPhone" runat="server" 
                                    ControlToValidate="txtContactPhone" ErrorMessage="رقم الهاتف مطلوب" 
                                    CssClass="error-message" Display="Dynamic" ValidationGroup="Step3"></asp:RequiredFieldValidator>
                            </div>

                            <div class="form-group half-width">
                                <label for="txtLocation">
                                    <i class="fas fa-map-marker-alt"></i> موقع السيارة *
                                </label>
                                <asp:TextBox ID="txtLocation" runat="server" CssClass="form-control" 
                                    placeholder="المدينة، المنطقة"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvLocation" runat="server" 
                                    ControlToValidate="txtLocation" ErrorMessage="موقع السيارة مطلوب" 
                                    CssClass="error-message" Display="Dynamic" ValidationGroup="Step3"></asp:RequiredFieldValidator>
                            </div>
                        </div>

                        <!-- Terms Agreement -->
                        <div class="form-group checkbox-group">
                            <asp:CheckBox ID="chkAgreement" runat="server" CssClass="form-checkbox" />
                            <label for="chkAgreement">
                                أوافق على <a href="AuctionTerms.aspx" target="_blank">شروط وأحكام المزاد</a> 
                                وأتحمل المسؤولية الكاملة عن صحة المعلومات المقدمة
                            </label>
                            <asp:CustomValidator ID="cvAgreement" runat="server" 
                                ErrorMessage="يجب الموافقة على الشروط والأحكام" 
                                CssClass="error-message" Display="Dynamic" 
                                ClientValidationFunction="validateAgreement" ValidationGroup="Step3"></asp:CustomValidator>
                        </div>

                        <div class="step-actions">
                            <button type="button" class="btn btn-outline" onclick="prevStep(3)">السابق</button>
                            <asp:Button ID="btnSubmit" runat="server" Text="إضافة السيارة للمزاد" 
                                CssClass="btn btn-success btn-large" OnClick="btnSubmit_Click" 
                                ValidationGroup="Step3" />
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="footer">
            <div class="container">
                <div class="footer-bottom">
                    <p>&copy; 2024 مزادات السيارات. جميع الحقوق محفوظة.</p>
                </div>
            </div>
        </footer>
    </form>

    <script src="js/main.js"></script>
    <script>
        let currentStep = 1;

        function nextStep(step) {
            if (validateStep(step)) {
                currentStep = step + 1;
                updateStepDisplay();
                return false; // Prevent postback
            }
            return true; // Allow validation to show errors
        }

        function prevStep(step) {
            currentStep = step - 1;
            updateStepDisplay();
        }

        function updateStepDisplay() {
            // Update step indicators
            document.querySelectorAll('.step').forEach((step, index) => {
                if (index + 1 <= currentStep) {
                    step.classList.add('active');
                } else {
                    step.classList.remove('active');
                }
            });

            // Update form steps
            document.querySelectorAll('.form-step').forEach((step, index) => {
                if (index + 1 === currentStep) {
                    step.classList.add('active');
                } else {
                    step.classList.remove('active');
                }
            });
        }

        function validateStep(step) {
            // This will be handled by ASP.NET validators
            return Page_ClientValidate('Step' + step);
        }

        function validateAgreement(sender, args) {
            const checkbox = document.getElementById('<%= chkAgreement.ClientID %>');
            args.IsValid = checkbox.checked;
        }

        // File upload preview
        document.getElementById('<%= fuCarImages.ClientID %>').addEventListener('change', function(e) {
            const files = e.target.files;
            const preview = document.querySelector('.file-upload-text p');
            if (files.length > 0) {
                preview.textContent = `تم اختيار ${files.length} صورة`;
            } else {
                preview.textContent = 'اسحب الصور هنا أو انقر للاختيار';
            }
        });
    </script>
</body>
</html>
