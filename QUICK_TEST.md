# اختبار سريع للمشروع

## خطوات الاختبار السريع

### 1. تشغيل المشروع
```
1. افتح Visual Studio 2022
2. افتح CarAuctionWebsite.sln
3. اضغط F5
```

### 2. إعد<PERSON> قاعدة البيانات
```sql
-- في SQL Server Management Studio أو Visual Studio
-- قم بتشغيل هذين الملفين بالترتيب:
1. CarAuctionWebsite\App_Data\CreateDatabase.sql
2. CarAuctionWebsite\App_Data\UpdatePasswords.sql
```

### 3. بيانات الاختبار

#### مستخدمين تجريبيين:
- **المستخدم 1**: ahmed123 / Password123
- **المستخدم 2**: fatima456 / Password123  
- **المستخدم 3**: mohammed789 / Password123

#### سيارات تجريبية:
- تويوتا كامري 2020 (85,000 ريال)
- هوندا أكورد 2019 (75,000 ريال)
- BMW X5 2021 (180,000 ريال)

### 4. اختبار الوظائف

#### أ. تسجيل مستخدم جديد
1. اذهب إلى صفحة التسجيل
2. أدخل البيانات المطلوبة
3. تأكد من نجاح التسجيل

#### ب. تسجيل الدخول
1. استخدم أحد المستخدمين التجريبيين
2. تأكد من ظهور رسالة الترحيب

#### ج. إضافة سيارة جديدة
1. سجل دخولك أولاً
2. اذهب إلى "إضافة سيارة"
3. املأ النموذج بالكامل
4. ارفع صورة للسيارة
5. تأكد من نجاح الإضافة

#### د. تصفح المزادات
1. اذهب إلى صفحة المزادات
2. جرب فلاتر البحث
3. تأكد من عرض النتائج

#### هـ. البحث والتصفية
1. ابحث عن "تويوتا"
2. صفي حسب السنة
3. صفي حسب السعر

### 5. التحقق من الأخطاء

#### إذا لم يعمل المشروع:
```
1. تحقق من Error List في Visual Studio
2. تأكد من تشغيل SQL Server LocalDB
3. تحقق من سلسلة الاتصال في Web.config
```

#### إذا لم تعمل قاعدة البيانات:
```
1. تأكد من تشغيل CreateDatabase.sql أولاً
2. تحقق من وجود قاعدة البيانات في SQL Server Object Explorer
3. قم بتشغيل UpdatePasswords.sql
```

#### إذا لم تعمل الصور:
```
1. تأكد من وجود مجلد images/cars/
2. تحقق من صلاحيات الكتابة
3. تأكد من أن حجم الصورة أقل من 5MB
```

### 6. نتائج الاختبار المتوقعة

✅ **يجب أن تعمل:**
- تسجيل مستخدم جديد
- تسجيل الدخول والخروج
- عرض الصفحة الرئيسية مع الإحصائيات
- تصفح المزادات
- البحث والتصفية
- إضافة سيارة جديدة (للمستخدمين المسجلين)

❌ **قد لا تعمل (مميزات مستقبلية):**
- المزايدة الفعلية (تحتاج تطوير إضافي)
- نظام الدفع
- الإشعارات الفورية
- رفع صور متعددة

### 7. ملاحظات مهمة

- المشروع في مرحلة التطوير
- بعض الوظائف تحتاج تطوير إضافي
- البيانات التجريبية للاختبار فقط
- كلمات المرور مشفرة بـ SHA256

### 8. إذا نجح الاختبار

🎉 **تهانينا!** المشروع يعمل بنجاح
- يمكنك الآن تطوير مميزات إضافية
- جرب إضافة بيانات أكثر
- اختبر على متصفحات مختلفة

### 9. للحصول على المساعدة

إذا واجهت مشاكل:
1. راجع ملف SETUP_INSTRUCTIONS.md
2. تحقق من نافذة Output في Visual Studio
3. راجع Error List للأخطاء التفصيلية
