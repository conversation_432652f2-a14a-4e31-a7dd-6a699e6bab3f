-- ===== UPDATE SAMPLE USER PASSWORDS =====
-- This script updates the sample user passwords to proper hashed values
-- Default password for all test users: "Password123"

-- Update user passwords with proper SHA256 hash
-- Password: "Password123" + Salt: "CarAuctionSalt"
UPDATE Users 
SET PasswordHash = 'a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3'
WHERE Username IN ('ahmed123', 'fatima456', 'mohammed789');

-- Verify the update
SELECT Username, FirstName, LastName, Email, 
       CASE WHEN PasswordHash = 'a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3' 
            THEN 'Password Updated' 
            ELSE 'Password NOT Updated' 
       END AS PasswordStatus
FROM Users 
WHERE Username IN ('ahmed123', 'fatima456', 'mohammed789');

PRINT 'Sample user passwords updated successfully!';
PRINT 'Default password for all test users is: Password123';
PRINT 'You can now login with:';
PRINT '- Username: ahmed123, Password: Password123';
PRINT '- Username: fatima456, Password: Password123';
PRINT '- Username: mohammed789, Password: Password123';
