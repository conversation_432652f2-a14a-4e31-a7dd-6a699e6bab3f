using System;
using System.Configuration;
using System.Data.SqlClient;
using System.IO;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace CarAuctionWebsite
{
    public partial class AddCar : System.Web.UI.Page
    {
        private string connectionString = ConfigurationManager.ConnectionStrings["CarAuctionDB"].ConnectionString;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // Check if user is logged in
                if (Session["UserId"] == null)
                {
                    Response.Redirect("Login.aspx?ReturnUrl=" + Request.Url.PathAndQuery);
                    return;
                }
                
                CheckUserSession();
                LoadYearDropDown();
            }
        }

        private void CheckUserSession()
        {
            if (Session["UserId"] != null)
            {
                // User is logged in
                lnkLogin.Visible = false;
                lnkRegister.Visible = false;
                userMenu.Visible = true;
                
                string firstName = Session["FirstName"]?.ToString() ?? "المستخدم";
                lblWelcome.Text = $"مرحباً، {firstName}";
            }
            else
            {
                // User is not logged in
                lnkLogin.Visible = true;
                lnkRegister.Visible = true;
                userMenu.Visible = false;
            }
        }

        private void LoadYearDropDown()
        {
            ddlYear.Items.Clear();
            ddlYear.Items.Add(new ListItem("اختر السنة", ""));
            
            int currentYear = DateTime.Now.Year;
            for (int year = currentYear; year >= 1990; year--)
            {
                ddlYear.Items.Add(new ListItem(year.ToString(), year.ToString()));
            }
        }

        protected void ddlBrand_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadModelDropDown();
        }

        private void LoadModelDropDown()
        {
            ddlModel.Items.Clear();
            ddlModel.Items.Add(new ListItem("اختر الموديل", ""));
            
            string selectedBrand = ddlBrand.SelectedValue;
            
            // Add models based on selected brand
            switch (selectedBrand)
            {
                case "Toyota":
                    ddlModel.Items.Add(new ListItem("كامري", "Camry"));
                    ddlModel.Items.Add(new ListItem("كورولا", "Corolla"));
                    ddlModel.Items.Add(new ListItem("برادو", "Prado"));
                    ddlModel.Items.Add(new ListItem("هايلكس", "Hilux"));
                    ddlModel.Items.Add(new ListItem("راف فور", "RAV4"));
                    break;
                case "Honda":
                    ddlModel.Items.Add(new ListItem("أكورد", "Accord"));
                    ddlModel.Items.Add(new ListItem("سيفيك", "Civic"));
                    ddlModel.Items.Add(new ListItem("سي آر في", "CR-V"));
                    ddlModel.Items.Add(new ListItem("بايلوت", "Pilot"));
                    break;
                case "Nissan":
                    ddlModel.Items.Add(new ListItem("التيما", "Altima"));
                    ddlModel.Items.Add(new ListItem("سنترا", "Sentra"));
                    ddlModel.Items.Add(new ListItem("باترول", "Patrol"));
                    ddlModel.Items.Add(new ListItem("إكس تريل", "X-Trail"));
                    break;
                case "BMW":
                    ddlModel.Items.Add(new ListItem("الفئة الثالثة", "3 Series"));
                    ddlModel.Items.Add(new ListItem("الفئة الخامسة", "5 Series"));
                    ddlModel.Items.Add(new ListItem("X3", "X3"));
                    ddlModel.Items.Add(new ListItem("X5", "X5"));
                    break;
                case "Mercedes":
                    ddlModel.Items.Add(new ListItem("C-Class", "C-Class"));
                    ddlModel.Items.Add(new ListItem("E-Class", "E-Class"));
                    ddlModel.Items.Add(new ListItem("GLC", "GLC"));
                    ddlModel.Items.Add(new ListItem("GLE", "GLE"));
                    break;
            }
        }

        protected void btnSubmit_Click(object sender, EventArgs e)
        {
            if (Page.IsValid && ValidateCustomFields())
            {
                if (SaveCarAndAuction())
                {
                    ShowMessage("تم إضافة السيارة للمزاد بنجاح! سيتم مراجعتها من قبل الإدارة.", "success");
                    
                    // Clear form
                    ClearForm();
                    
                    // Redirect after 3 seconds
                    Response.AddHeader("REFRESH", "3;URL=Auctions.aspx");
                }
            }
        }

        private bool ValidateCustomFields()
        {
            bool isValid = true;

            // Validate file upload
            if (!fuCarImages.HasFile)
            {
                ShowMessage("يجب رفع صورة واحدة على الأقل للسيارة", "error");
                isValid = false;
            }
            else
            {
                // Validate file types and sizes
                if (fuCarImages.PostedFile != null && fuCarImages.PostedFile.ContentLength > 0)
                {
                    if (fuCarImages.PostedFile.ContentLength > 5 * 1024 * 1024) // 5MB
                    {
                        ShowMessage("حجم الصورة يجب أن يكون أقل من 5MB", "error");
                        isValid = false;
                    }

                    string extension = Path.GetExtension(fuCarImages.PostedFile.FileName).ToLower();
                    if (extension != ".jpg" && extension != ".jpeg" && extension != ".png" && extension != ".gif")
                    {
                        ShowMessage("نوع الملف غير مدعوم. يرجى رفع صور بصيغة JPG, PNG, أو GIF", "error");
                        isValid = false;
                    }
                }
            }
            
            // Validate reserve price
            if (!string.IsNullOrEmpty(txtReservePrice.Text))
            {
                decimal startingPrice = Convert.ToDecimal(txtStartingPrice.Text);
                decimal reservePrice = Convert.ToDecimal(txtReservePrice.Text);
                
                if (reservePrice < startingPrice)
                {
                    ShowMessage("السعر المحجوز يجب أن يكون أكبر من أو يساوي السعر الابتدائي", "error");
                    isValid = false;
                }
            }
            
            return isValid;
        }

        private bool SaveCarAndAuction()
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    using (SqlTransaction transaction = conn.BeginTransaction())
                    {
                        try
                        {
                            // Insert car
                            int carId = InsertCar(conn, transaction);
                            
                            // Upload and save images
                            string mainImageUrl = SaveCarImages(carId);
                            
                            // Update car with main image URL
                            UpdateCarMainImage(conn, transaction, carId, mainImageUrl);
                            
                            // Insert auction
                            InsertAuction(conn, transaction, carId);
                            
                            transaction.Commit();
                            return true;
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving car and auction: {ex.Message}");
                ShowMessage("حدث خطأ أثناء حفظ البيانات. يرجى المحاولة مرة أخرى.", "error");
                return false;
            }
        }

        private int InsertCar(SqlConnection conn, SqlTransaction transaction)
        {
            string features = GetSelectedFeatures();
            
            string query = @"
                INSERT INTO Cars (
                    UserId, Brand, Model, Year, Mileage, FuelType, Transmission, 
                    Color, EngineSize, Condition, Description, Features, 
                    ContactPhone, Location, CreatedDate, IsActive
                ) VALUES (
                    @UserId, @Brand, @Model, @Year, @Mileage, @FuelType, @Transmission, 
                    @Color, @EngineSize, @Condition, @Description, @Features, 
                    @ContactPhone, @Location, GETDATE(), 1
                );
                SELECT SCOPE_IDENTITY();";

            using (SqlCommand cmd = new SqlCommand(query, conn, transaction))
            {
                cmd.Parameters.AddWithValue("@UserId", Session["UserId"]);
                cmd.Parameters.AddWithValue("@Brand", ddlBrand.SelectedValue);
                cmd.Parameters.AddWithValue("@Model", ddlModel.SelectedValue);
                cmd.Parameters.AddWithValue("@Year", ddlYear.SelectedValue);
                cmd.Parameters.AddWithValue("@Mileage", txtMileage.Text);
                cmd.Parameters.AddWithValue("@FuelType", ddlFuelType.SelectedValue);
                cmd.Parameters.AddWithValue("@Transmission", ddlTransmission.SelectedValue);
                cmd.Parameters.AddWithValue("@Color", ddlColor.SelectedValue);
                cmd.Parameters.AddWithValue("@EngineSize", 
                    string.IsNullOrEmpty(txtEngineSize.Text) ? (object)DBNull.Value : txtEngineSize.Text);
                cmd.Parameters.AddWithValue("@Condition", ddlCondition.SelectedValue);
                cmd.Parameters.AddWithValue("@Description", txtDescription.Text.Trim());
                cmd.Parameters.AddWithValue("@Features", features);
                cmd.Parameters.AddWithValue("@ContactPhone", txtContactPhone.Text.Trim());
                cmd.Parameters.AddWithValue("@Location", txtLocation.Text.Trim());
                
                return Convert.ToInt32(cmd.ExecuteScalar());
            }
        }

        private string SaveCarImages(int carId)
        {
            string mainImageUrl = "";
            string uploadPath = Server.MapPath("~/images/cars/");

            // Create directory if it doesn't exist
            if (!Directory.Exists(uploadPath))
            {
                Directory.CreateDirectory(uploadPath);
            }

            if (fuCarImages.HasFile && fuCarImages.PostedFile.ContentLength > 0)
            {
                string fileName = $"car_{carId}_{DateTime.Now.Ticks}{Path.GetExtension(fuCarImages.PostedFile.FileName)}";
                string filePath = Path.Combine(uploadPath, fileName);

                fuCarImages.PostedFile.SaveAs(filePath);

                mainImageUrl = "images/cars/" + fileName;

                // Save image record to database
                SaveCarImageRecord(carId, mainImageUrl, 1, true);
            }

            return mainImageUrl;
        }

        private void SaveCarImageRecord(int carId, string imageUrl, int imageOrder, bool isMain)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    string query = @"
                        INSERT INTO CarImages (CarId, ImageUrl, ImageOrder, IsMain, UploadedDate)
                        VALUES (@CarId, @ImageUrl, @ImageOrder, @IsMain, GETDATE())";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@CarId", carId);
                        cmd.Parameters.AddWithValue("@ImageUrl", imageUrl);
                        cmd.Parameters.AddWithValue("@ImageOrder", imageOrder);
                        cmd.Parameters.AddWithValue("@IsMain", isMain);
                        
                        conn.Open();
                        cmd.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving image record: {ex.Message}");
            }
        }

        private void UpdateCarMainImage(SqlConnection conn, SqlTransaction transaction, int carId, string imageUrl)
        {
            string query = "UPDATE Cars SET ImageUrl = @ImageUrl WHERE CarId = @CarId";
            
            using (SqlCommand cmd = new SqlCommand(query, conn, transaction))
            {
                cmd.Parameters.AddWithValue("@ImageUrl", imageUrl);
                cmd.Parameters.AddWithValue("@CarId", carId);
                cmd.ExecuteNonQuery();
            }
        }

        private void InsertAuction(SqlConnection conn, SqlTransaction transaction, int carId)
        {
            DateTime startDateTime = DateTime.Now;
            DateTime endDateTime = startDateTime.AddDays(Convert.ToInt32(ddlDuration.SelectedValue));
            
            string query = @"
                INSERT INTO Auctions (
                    CarId, StartingPrice, ReservePrice, CurrentBid, 
                    StartDateTime, EndDateTime, Status, CreatedDate
                ) VALUES (
                    @CarId, @StartingPrice, @ReservePrice, @CurrentBid, 
                    @StartDateTime, @EndDateTime, 'Pending', GETDATE()
                )";

            using (SqlCommand cmd = new SqlCommand(query, conn, transaction))
            {
                cmd.Parameters.AddWithValue("@CarId", carId);
                cmd.Parameters.AddWithValue("@StartingPrice", txtStartingPrice.Text);
                cmd.Parameters.AddWithValue("@ReservePrice", 
                    string.IsNullOrEmpty(txtReservePrice.Text) ? (object)DBNull.Value : txtReservePrice.Text);
                cmd.Parameters.AddWithValue("@CurrentBid", txtStartingPrice.Text);
                cmd.Parameters.AddWithValue("@StartDateTime", startDateTime);
                cmd.Parameters.AddWithValue("@EndDateTime", endDateTime);
                
                cmd.ExecuteNonQuery();
            }
        }

        private string GetSelectedFeatures()
        {
            var selectedFeatures = new System.Collections.Generic.List<string>();
            
            foreach (ListItem item in cblFeatures.Items)
            {
                if (item.Selected)
                {
                    selectedFeatures.Add(item.Value);
                }
            }
            
            return string.Join(",", selectedFeatures);
        }

        private void ClearForm()
        {
            // Clear all form fields
            ddlBrand.SelectedIndex = 0;
            ddlModel.Items.Clear();
            ddlModel.Items.Add(new ListItem("اختر الموديل", ""));
            ddlYear.SelectedIndex = 0;
            txtMileage.Text = "";
            ddlFuelType.SelectedIndex = 0;
            ddlTransmission.SelectedIndex = 0;
            ddlColor.SelectedIndex = 0;
            txtEngineSize.Text = "";
            ddlCondition.SelectedIndex = 0;
            txtDescription.Text = "";
            txtStartingPrice.Text = "";
            txtReservePrice.Text = "";
            ddlDuration.SelectedIndex = 0;
            txtContactPhone.Text = "";
            txtLocation.Text = "";
            chkAgreement.Checked = false;
            
            // Clear features
            foreach (ListItem item in cblFeatures.Items)
            {
                item.Selected = false;
            }
        }

        protected void lnkLogout_Click(object sender, EventArgs e)
        {
            Session.Clear();
            Session.Abandon();
            Response.Redirect("Default.aspx");
        }

        private void ShowMessage(string message, string type)
        {
            lblMessage.Text = message;
            pnlMessage.CssClass = $"message-panel {type}";
            pnlMessage.Visible = true;
        }
    }
}
