# تعليمات تشغيل مشروع مزادات السيارات

## المتطلبات الأساسية

### 1. البرامج المطلوبة
- **Visual Studio 2022** (Community أو Professional)
- **SQL Server LocalDB** (مدمج مع Visual Studio)
- **.NET Framework 4.8** (مدمج مع Visual Studio)

### 2. التحقق من التثبيت
تأكد من وجود المكونات التالية:
- IIS Express (مدمج مع Visual Studio)
- SQL Server LocalDB (مدمج مع Visual Studio)

## خطوات التشغيل

### الخطوة 1: فتح المشروع
1. افتح Visual Studio 2022
2. اختر **File > Open > Project/Solution**
3. انتقل إلى مجلد المشروع واختر `CarAuctionWebsite.sln`

### الخطوة 2: إعداد قاعدة البيانات

#### الطريقة الأولى: استخدام SQL Server Management Studio
1. افتح **SQL Server Management Studio (SSMS)**
2. اتصل بـ `(LocalDB)\MSSQLLocalDB`
3. افتح ملف `CarAuctionWebsite\App_Data\CreateDatabase.sql`
4. قم بتشغيل السكريبت (F5)

#### الطريقة الثانية: استخدام Visual Studio
1. في Visual Studio، اذهب إلى **View > SQL Server Object Explorer**
2. اتصل بـ `(LocalDB)\MSSQLLocalDB`
3. انقر بالزر الأيمن على **Databases** واختر **Add New Database**
4. أدخل اسم `CarAuctionDB`
5. افتح ملف `CreateDatabase.sql` وقم بتشغيله

### الخطوة 3: التحقق من سلسلة الاتصال
تأكد من أن سلسلة الاتصال في `Web.config` صحيحة:
```xml
<connectionStrings>
    <add name="CarAuctionDB" 
         connectionString="Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\CarAuctionDB.mdf;Integrated Security=True" 
         providerName="System.Data.SqlClient" />
</connectionStrings>
```

### الخطوة 4: تشغيل المشروع
1. في Visual Studio، تأكد من أن `CarAuctionWebsite` هو المشروع الافتراضي
2. اضغط **F5** أو **Ctrl+F5** لتشغيل المشروع
3. سيتم فتح المتصفح تلقائياً على الصفحة الرئيسية

## اختبار المشروع

### 1. البيانات التجريبية
المشروع يحتوي على بيانات تجريبية:
- **المستخدمين**: ahmed123, fatima456, mohammed789
- **كلمة المرور**: hashed_password (يجب تحديثها)
- **السيارات**: 3 سيارات نموذجية
- **المزادات**: 3 مزادات نشطة

### 2. اختبار الوظائف
1. **التسجيل**: سجل مستخدم جديد
2. **تسجيل الدخول**: سجل دخول بالمستخدم الجديد
3. **إضافة سيارة**: أضف سيارة جديدة للمزاد
4. **تصفح المزادات**: تصفح المزادات المتاحة
5. **البحث والتصفية**: جرب خيارات البحث

## حل المشاكل الشائعة

### مشكلة 1: خطأ في الاتصال بقاعدة البيانات
**الحل:**
1. تأكد من تشغيل SQL Server LocalDB
2. تحقق من سلسلة الاتصال في Web.config
3. تأكد من إنشاء قاعدة البيانات بنجاح

### مشكلة 2: خطأ في تشغيل المشروع
**الحل:**
1. تأكد من أن .NET Framework 4.8 مثبت
2. قم بـ Clean Solution ثم Rebuild Solution
3. تحقق من أن جميع المراجع موجودة

### مشكلة 3: خطأ في رفع الصور
**الحل:**
1. تأكد من وجود مجلد `images/cars/`
2. تحقق من صلاحيات الكتابة في المجلد
3. تأكد من أن حجم الملف أقل من 5MB

### مشكلة 4: خطأ في عرض الصفحات
**الحل:**
1. تحقق من أن IIS Express يعمل
2. تأكد من أن المنفذ غير مستخدم
3. جرب تشغيل Visual Studio كمدير

## ملاحظات مهمة

### 1. الأمان
- كلمات المرور في البيانات التجريبية مشفرة
- لإنشاء مستخدم جديد، استخدم صفحة التسجيل
- تأكد من تحديث كلمات المرور الافتراضية

### 2. الأداء
- المشروع محسن للتطوير المحلي
- لنشر الإنتاج، قم بتحديث سلسلة الاتصال
- فعل التخزين المؤقت للإنتاج

### 3. التطوير
- استخدم وضع Debug للتطوير
- تحقق من نافذة Output للأخطاء
- استخدم Breakpoints لتتبع الأخطاء

## الدعم الفني

إذا واجهت أي مشاكل:
1. تحقق من نافذة **Error List** في Visual Studio
2. راجع نافذة **Output** للتفاصيل
3. تحقق من **SQL Server Object Explorer** لقاعدة البيانات

## الخطوات التالية

بعد تشغيل المشروع بنجاح:
1. جرب جميع الوظائف
2. أضف بيانات تجريبية إضافية
3. اختبر على متصفحات مختلفة
4. راجع الكود لفهم البنية

---

**ملاحظة**: هذا المشروع تعليمي ويحتاج إلى تحسينات إضافية للاستخدام في الإنتاج.
