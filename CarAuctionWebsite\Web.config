<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <connectionStrings>
    <add name="CarAuctionDB" connectionString="Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\CarAuctionDB.mdf;Integrated Security=True" providerName="System.Data.SqlClient" />
  </connectionStrings>

  <appSettings>
    <add key="vs:EnableBrowserLink" value="false" />
  </appSettings>

  <system.web>
    <compilation debug="true" targetFramework="4.8" />
    <httpRuntime targetFramework="4.8" requestValidationMode="2.0" />
    <pages controlRenderingCompatibilityVersion="4.0" validateRequest="false">
      <controls>
        <add tagPrefix="asp" namespace="System.Web.UI" assembly="System.Web.Extensions, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
      </controls>
    </pages>
    <globalization culture="ar-SA" uiCulture="ar-SA" requestEncoding="utf-8" responseEncoding="utf-8" fileEncoding="utf-8" />
  </system.web>

  <system.webServer>
    <defaultDocument>
      <files>
        <clear />
        <add value="Default.aspx" />
      </files>
    </defaultDocument>
  </system.webServer>
</configuration>
