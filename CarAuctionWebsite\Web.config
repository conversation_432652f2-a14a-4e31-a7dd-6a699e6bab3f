<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <connectionStrings>
    <add name="CarAuctionDB" connectionString="Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\CarAuctionDB.mdf;Integrated Security=True" providerName="System.Data.SqlClient" />
  </connectionStrings>
  
  <appSettings>
    <add key="vs:EnableBrowserLink" value="false" />
  </appSettings>
  
  <system.web>
    <compilation debug="true" targetFramework="4.8" />
    <httpRuntime targetFramework="4.8" maxRequestLength="51200" executionTimeout="3600" />

    <!-- Session configuration -->
    <sessionState mode="InProc" timeout="30" />

    <!-- Authentication configuration -->
    <authentication mode="Forms">
      <forms loginUrl="Login.aspx" defaultUrl="Default.aspx" timeout="30" />
    </authentication>

    <!-- Authorization configuration -->
    <authorization>
      <allow users="*" />
    </authorization>

    <!-- Custom errors -->
    <customErrors mode="RemoteOnly" defaultRedirect="Error.aspx" />

    <!-- Pages configuration -->
    <pages controlRenderingCompatibilityVersion="4.0" validateRequest="false" />

    <!-- Trust level -->
    <trust level="Full" />
  </system.web>
  
  <system.webServer>
    <defaultDocument>
      <files>
        <clear />
        <add value="Default.aspx" />
      </files>
    </defaultDocument>
    
    <!-- Static file handling -->
    <staticContent>
      <mimeMap fileExtension=".woff" mimeType="application/font-woff" />
      <mimeMap fileExtension=".woff2" mimeType="application/font-woff2" />
    </staticContent>
  </system.webServer>
  
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <!-- Assembly binding redirects if needed -->
    </assemblyBinding>
  </runtime>
</configuration>
