<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Auctions.aspx.cs" Inherits="CarAuctionWebsite.Auctions" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ar" dir="rtl">
<head runat="server">
    <meta charset="utf-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Content-Language" content="ar" />
    <title>المزادات - مزادات السيارات</title>
    <link href="css/style.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet" />
</head>
<body>
    <form id="form1" runat="server">
        <!-- Header -->
        <header class="header">
            <nav class="navbar">
                <div class="nav-container">
                    <div class="nav-logo">
                        <a href="Default.aspx">
                            <h2><i class="fas fa-car"></i> مزادات السيارات</h2>
                        </a>
                    </div>
                    <ul class="nav-menu">
                        <li class="nav-item">
                            <a href="Default.aspx" class="nav-link">الرئيسية</a>
                        </li>
                        <li class="nav-item">
                            <a href="Auctions.aspx" class="nav-link active">المزادات</a>
                        </li>
                        <li class="nav-item">
                            <a href="AddCar.aspx" class="nav-link">إضافة سيارة</a>
                        </li>
                        <li class="nav-item">
                            <asp:LinkButton ID="lnkLogin" runat="server" CssClass="nav-link" PostBackUrl="Login.aspx">تسجيل الدخول</asp:LinkButton>
                        </li>
                        <li class="nav-item">
                            <asp:LinkButton ID="lnkRegister" runat="server" CssClass="nav-link btn-register" PostBackUrl="Register.aspx">التسجيل</asp:LinkButton>
                        </li>
                        <li class="nav-item" id="userMenu" runat="server" visible="false">
                            <asp:Label ID="lblWelcome" runat="server" CssClass="nav-link"></asp:Label>
                            <asp:LinkButton ID="lnkLogout" runat="server" CssClass="nav-link" OnClick="lnkLogout_Click">تسجيل الخروج</asp:LinkButton>
                        </li>
                    </ul>
                    <div class="hamburger">
                        <span class="bar"></span>
                        <span class="bar"></span>
                        <span class="bar"></span>
                    </div>
                </div>
            </nav>
        </header>

        <!-- Page Header -->
        <section class="page-header">
            <div class="container">
                <h1><i class="fas fa-gavel"></i> المزادات النشطة</h1>
                <p>تصفح جميع المزادات المتاحة واختر السيارة المناسبة لك</p>
            </div>
        </section>

        <!-- Filters Section -->
        <section class="filters-section">
            <div class="container">
                <div class="filters-card">
                    <h3><i class="fas fa-filter"></i> تصفية النتائج</h3>
                    <div class="filters-grid">
                        <!-- Search -->
                        <div class="filter-group">
                            <label>البحث</label>
                            <asp:TextBox ID="txtSearch" runat="server" CssClass="form-control" 
                                placeholder="ابحث عن سيارة..." AutoPostBack="true" OnTextChanged="ApplyFilters"></asp:TextBox>
                        </div>

                        <!-- Brand -->
                        <div class="filter-group">
                            <label>الماركة</label>
                            <asp:DropDownList ID="ddlBrand" runat="server" CssClass="form-control" 
                                AutoPostBack="true" OnSelectedIndexChanged="ApplyFilters">
                                <asp:ListItem Value="">جميع الماركات</asp:ListItem>
                                <asp:ListItem Value="Toyota">تويوتا</asp:ListItem>
                                <asp:ListItem Value="Honda">هوندا</asp:ListItem>
                                <asp:ListItem Value="Nissan">نيسان</asp:ListItem>
                                <asp:ListItem Value="Hyundai">هيونداي</asp:ListItem>
                                <asp:ListItem Value="BMW">بي إم دبليو</asp:ListItem>
                                <asp:ListItem Value="Mercedes">مرسيدس</asp:ListItem>
                                <asp:ListItem Value="Audi">أودي</asp:ListItem>
                                <asp:ListItem Value="Lexus">لكزس</asp:ListItem>
                            </asp:DropDownList>
                        </div>

                        <!-- Year -->
                        <div class="filter-group">
                            <label>السنة</label>
                            <asp:DropDownList ID="ddlYear" runat="server" CssClass="form-control" 
                                AutoPostBack="true" OnSelectedIndexChanged="ApplyFilters">
                                <asp:ListItem Value="">جميع السنوات</asp:ListItem>
                            </asp:DropDownList>
                        </div>

                        <!-- Price Range -->
                        <div class="filter-group">
                            <label>نطاق السعر</label>
                            <asp:DropDownList ID="ddlPriceRange" runat="server" CssClass="form-control" 
                                AutoPostBack="true" OnSelectedIndexChanged="ApplyFilters">
                                <asp:ListItem Value="">جميع الأسعار</asp:ListItem>
                                <asp:ListItem Value="0-50000">أقل من 50,000 ريال</asp:ListItem>
                                <asp:ListItem Value="50000-100000">50,000 - 100,000 ريال</asp:ListItem>
                                <asp:ListItem Value="100000-200000">100,000 - 200,000 ريال</asp:ListItem>
                                <asp:ListItem Value="200000-500000">200,000 - 500,000 ريال</asp:ListItem>
                                <asp:ListItem Value="500000-999999999">أكثر من 500,000 ريال</asp:ListItem>
                            </asp:DropDownList>
                        </div>

                        <!-- Status -->
                        <div class="filter-group">
                            <label>حالة المزاد</label>
                            <asp:DropDownList ID="ddlStatus" runat="server" CssClass="form-control" 
                                AutoPostBack="true" OnSelectedIndexChanged="ApplyFilters">
                                <asp:ListItem Value="">جميع الحالات</asp:ListItem>
                                <asp:ListItem Value="Active">نشط</asp:ListItem>
                                <asp:ListItem Value="Ending Soon">ينتهي قريباً</asp:ListItem>
                                <asp:ListItem Value="Completed">مكتمل</asp:ListItem>
                            </asp:DropDownList>
                        </div>

                        <!-- Clear Filters -->
                        <div class="filter-group">
                            <asp:Button ID="btnClearFilters" runat="server" Text="مسح الفلاتر" 
                                CssClass="btn btn-outline" OnClick="btnClearFilters_Click" />
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Auctions Grid -->
        <section class="auctions-section">
            <div class="container">
                <!-- Results Info -->
                <div class="results-info">
                    <asp:Label ID="lblResultsCount" runat="server" CssClass="results-count"></asp:Label>
                    <div class="sort-options">
                        <label>ترتيب حسب:</label>
                        <asp:DropDownList ID="ddlSort" runat="server" CssClass="form-control" 
                            AutoPostBack="true" OnSelectedIndexChanged="ApplyFilters">
                            <asp:ListItem Value="EndDate">تاريخ الانتهاء</asp:ListItem>
                            <asp:ListItem Value="CurrentBid">السعر الحالي</asp:ListItem>
                            <asp:ListItem Value="Year">سنة الصنع</asp:ListItem>
                            <asp:ListItem Value="Brand">الماركة</asp:ListItem>
                        </asp:DropDownList>
                    </div>
                </div>

                <!-- Auctions Grid -->
                <div class="auctions-grid">
                    <asp:Repeater ID="rptAuctions" runat="server">
                        <ItemTemplate>
                            <div class="auction-card">
                                <div class="auction-image">
                                    <img src='<%# Eval("ImageUrl") %>' alt='<%# Eval("CarModel") %>' />
                                    <div class="auction-status">
                                        <span class="status-badge <%# Eval("StatusClass") %>"><%# Eval("Status") %></span>
                                    </div>
                                    <div class="auction-timer" data-endtime='<%# Eval("EndDateTime") %>'>
                                        <i class="fas fa-clock"></i>
                                        <span class="timer-text"><%# Eval("TimeLeft") %></span>
                                    </div>
                                </div>
                                <div class="auction-info">
                                    <h3><%# Eval("CarModel") %></h3>
                                    <p class="auction-details">
                                        <span><i class="fas fa-calendar"></i> <%# Eval("Year") %></span>
                                        <span><i class="fas fa-road"></i> <%# Eval("Mileage") %> كم</span>
                                        <span><i class="fas fa-gas-pump"></i> <%# Eval("FuelType") %></span>
                                    </p>
                                    <div class="auction-price">
                                        <div class="current-bid">
                                            <span class="label">السعر الحالي:</span>
                                            <span class="amount"><%# Eval("CurrentBid") %> ريال</span>
                                        </div>
                                        <div class="bid-count">
                                            <i class="fas fa-users"></i> <%# Eval("BidCount") %> مزايد
                                        </div>
                                    </div>
                                    <div class="auction-actions">
                                        <a href='<%# "AuctionDetails.aspx?id=" + Eval("AuctionId") %>' class="btn btn-primary">
                                            <i class="fas fa-eye"></i> عرض التفاصيل
                                        </a>
                                        <asp:LinkButton ID="btnPlaceBid" runat="server" CssClass="btn btn-success" 
                                            CommandArgument='<%# Eval("AuctionId") %>' OnClick="btnPlaceBid_Click"
                                            Visible='<%# Eval("CanBid") %>'>
                                            <i class="fas fa-gavel"></i> مزايدة
                                        </asp:LinkButton>
                                    </div>
                                </div>
                            </div>
                        </ItemTemplate>
                    </asp:Repeater>
                </div>

                <!-- No Results -->
                <asp:Panel ID="pnlNoResults" runat="server" Visible="false" CssClass="no-results">
                    <div class="no-results-content">
                        <i class="fas fa-search"></i>
                        <h3>لا توجد نتائج</h3>
                        <p>لم نجد أي مزادات تطابق معايير البحث الخاصة بك</p>
                        <asp:Button ID="btnResetSearch" runat="server" Text="إعادة تعيين البحث" 
                            CssClass="btn btn-primary" OnClick="btnClearFilters_Click" />
                    </div>
                </asp:Panel>

                <!-- Pagination -->
                <div class="pagination-container">
                    <!-- Pagination will be implemented later -->
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="footer">
            <div class="container">
                <div class="footer-bottom">
                    <p>&copy; 2024 مزادات السيارات. جميع الحقوق محفوظة.</p>
                </div>
            </div>
        </footer>
    </form>

    <script src="js/main.js"></script>
    <script>
        // Timer functionality for auction countdown
        function updateTimers() {
            const timers = document.querySelectorAll('.auction-timer');
            timers.forEach(timer => {
                const endTime = new Date(timer.dataset.endtime).getTime();
                const now = new Date().getTime();
                const timeLeft = endTime - now;

                if (timeLeft > 0) {
                    const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
                    const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

                    let timeText = '';
                    if (days > 0) {
                        timeText = `${days}د ${hours}س`;
                    } else if (hours > 0) {
                        timeText = `${hours}س ${minutes}د`;
                    } else {
                        timeText = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                    }

                    timer.querySelector('.timer-text').textContent = timeText;
                } else {
                    timer.querySelector('.timer-text').textContent = 'انتهى';
                    timer.classList.add('expired');
                }
            });
        }

        // Update timers every second
        setInterval(updateTimers, 1000);
        updateTimers(); // Initial call
    </script>
</body>
</html>
