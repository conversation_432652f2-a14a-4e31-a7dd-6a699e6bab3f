// ===== GLOBAL VARIABLES =====
let currentUser = null;
let auctionTimers = [];

// ===== DOM READY =====
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// ===== INITIALIZATION =====
function initializeApp() {
    initializeNavigation();
    initializeTimers();
    initializeFormValidation();
    initializeImageUpload();
    initializeTooltips();
    checkUserSession();
}

// ===== NAVIGATION =====
function initializeNavigation() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');

    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });

        // Close menu when clicking on a link
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', () => {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!hamburger.contains(e.target) && !navMenu.contains(e.target)) {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
            }
        });
    }
}

// ===== AUCTION TIMERS =====
function initializeTimers() {
    updateAuctionTimers();
    setInterval(updateAuctionTimers, 1000);
}

function updateAuctionTimers() {
    const timers = document.querySelectorAll('.auction-timer[data-endtime]');
    
    timers.forEach(timer => {
        const endTime = new Date(timer.dataset.endtime).getTime();
        const now = new Date().getTime();
        const timeLeft = endTime - now;

        if (timeLeft > 0) {
            const timeString = formatTimeLeft(timeLeft);
            const timerText = timer.querySelector('.timer-text');
            if (timerText) {
                timerText.textContent = timeString;
            }

            // Add warning class if less than 1 hour left
            if (timeLeft < 3600000) { // 1 hour in milliseconds
                timer.classList.add('warning');
            }
        } else {
            timer.classList.add('expired');
            const timerText = timer.querySelector('.timer-text');
            if (timerText) {
                timerText.textContent = 'انتهى';
            }
        }
    });
}

function formatTimeLeft(timeLeft) {
    const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
    const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

    if (days > 0) {
        return `${days}د ${hours}س`;
    } else if (hours > 0) {
        return `${hours}س ${minutes}د`;
    } else if (minutes > 0) {
        return `${minutes}د ${seconds}ث`;
    } else {
        return `${seconds}ث`;
    }
}

// ===== FORM VALIDATION =====
function initializeFormValidation() {
    // Real-time validation for forms
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, select, textarea');
        
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });

            input.addEventListener('input', function() {
                clearFieldError(this);
            });
        });
    });
}

function validateField(field) {
    const value = field.value.trim();
    const fieldType = field.type;
    const fieldName = field.name || field.id;
    
    // Clear previous errors
    clearFieldError(field);
    
    // Required field validation
    if (field.hasAttribute('required') && !value) {
        showFieldError(field, 'هذا الحقل مطلوب');
        return false;
    }
    
    // Email validation
    if (fieldType === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            showFieldError(field, 'صيغة البريد الإلكتروني غير صحيحة');
            return false;
        }
    }
    
    // Phone validation (Saudi format)
    if (fieldName.includes('phone') || fieldName.includes('Phone')) {
        const phoneRegex = /^(05|5)[0-9]{8}$/;
        if (value && !phoneRegex.test(value)) {
            showFieldError(field, 'رقم الهاتف غير صحيح (يجب أن يبدأ بـ 05)');
            return false;
        }
    }
    
    // Password validation
    if (fieldType === 'password' && value) {
        if (value.length < 8) {
            showFieldError(field, 'كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل');
            return false;
        }
        
        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/;
        if (!passwordRegex.test(value)) {
            showFieldError(field, 'كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم');
            return false;
        }
    }
    
    // Number validation
    if (fieldType === 'number' && value) {
        const min = field.getAttribute('min');
        const max = field.getAttribute('max');
        
        if (min && parseFloat(value) < parseFloat(min)) {
            showFieldError(field, `القيمة يجب أن تكون أكبر من ${min}`);
            return false;
        }
        
        if (max && parseFloat(value) > parseFloat(max)) {
            showFieldError(field, `القيمة يجب أن تكون أقل من ${max}`);
            return false;
        }
    }
    
    return true;
}

function showFieldError(field, message) {
    clearFieldError(field);
    
    field.classList.add('error');
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

function clearFieldError(field) {
    field.classList.remove('error');
    
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
}

// ===== IMAGE UPLOAD =====
function initializeImageUpload() {
    const fileInputs = document.querySelectorAll('input[type="file"]');
    
    fileInputs.forEach(input => {
        input.addEventListener('change', function(e) {
            handleFileUpload(e);
        });
    });
    
    // Drag and drop functionality
    const uploadAreas = document.querySelectorAll('.file-upload-area');
    
    uploadAreas.forEach(area => {
        area.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });
        
        area.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });
        
        area.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            const fileInput = this.querySelector('input[type="file"]');
            
            if (fileInput && files.length > 0) {
                fileInput.files = files;
                handleFileUpload({ target: fileInput });
            }
        });
    });
}

function handleFileUpload(e) {
    const files = e.target.files;
    const uploadArea = e.target.closest('.file-upload-area');
    const uploadText = uploadArea ? uploadArea.querySelector('.file-upload-text p') : null;
    
    if (files.length > 0) {
        // Validate file types and sizes
        const validFiles = [];
        const maxSize = 5 * 1024 * 1024; // 5MB
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        
        for (let file of files) {
            if (!allowedTypes.includes(file.type)) {
                showNotification('نوع الملف غير مدعوم. يرجى اختيار صور بصيغة JPG, PNG, أو GIF', 'error');
                continue;
            }
            
            if (file.size > maxSize) {
                showNotification(`حجم الملف ${file.name} كبير جداً. الحد الأقصى 5MB`, 'error');
                continue;
            }
            
            validFiles.push(file);
        }
        
        if (validFiles.length > 0) {
            if (uploadText) {
                uploadText.textContent = `تم اختيار ${validFiles.length} صورة`;
            }
            
            // Create preview if needed
            createImagePreview(validFiles, uploadArea);
        }
    }
}

function createImagePreview(files, container) {
    // Remove existing preview
    const existingPreview = container.querySelector('.image-preview');
    if (existingPreview) {
        existingPreview.remove();
    }
    
    const previewContainer = document.createElement('div');
    previewContainer.className = 'image-preview';
    
    Array.from(files).slice(0, 5).forEach(file => { // Show max 5 previews
        const reader = new FileReader();
        reader.onload = function(e) {
            const img = document.createElement('img');
            img.src = e.target.result;
            img.className = 'preview-image';
            img.style.cssText = 'width: 60px; height: 60px; object-fit: cover; border-radius: 8px; margin: 5px;';
            previewContainer.appendChild(img);
        };
        reader.readAsDataURL(file);
    });
    
    container.appendChild(previewContainer);
}

// ===== TOOLTIPS =====
function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', function() {
            showTooltip(this);
        });
        
        element.addEventListener('mouseleave', function() {
            hideTooltip();
        });
    });
}

function showTooltip(element) {
    const tooltipText = element.getAttribute('data-tooltip');
    
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = tooltipText;
    tooltip.style.cssText = `
        position: absolute;
        background: #333;
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 14px;
        z-index: 1000;
        pointer-events: none;
        white-space: nowrap;
    `;
    
    document.body.appendChild(tooltip);
    
    const rect = element.getBoundingClientRect();
    tooltip.style.top = (rect.top - tooltip.offsetHeight - 5) + 'px';
    tooltip.style.left = (rect.left + rect.width / 2 - tooltip.offsetWidth / 2) + 'px';
}

function hideTooltip() {
    const tooltip = document.querySelector('.tooltip');
    if (tooltip) {
        tooltip.remove();
    }
}

// ===== NOTIFICATIONS =====
function showNotification(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
    `;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#007bff'};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        z-index: 10000;
        max-width: 400px;
        animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    if (duration > 0) {
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, duration);
    }
}

// ===== USER SESSION =====
function checkUserSession() {
    // Check if user is logged in (this would typically check a session or token)
    const userInfo = localStorage.getItem('userInfo');
    if (userInfo) {
        currentUser = JSON.parse(userInfo);
        updateUserInterface();
    }
}

function updateUserInterface() {
    if (currentUser) {
        // Update navigation to show user menu
        const loginLink = document.getElementById('lnkLogin');
        const registerLink = document.getElementById('lnkRegister');
        const userMenu = document.getElementById('userMenu');
        const welcomeLabel = document.getElementById('lblWelcome');
        
        if (loginLink) loginLink.style.display = 'none';
        if (registerLink) registerLink.style.display = 'none';
        if (userMenu) userMenu.style.display = 'block';
        if (welcomeLabel) welcomeLabel.textContent = `مرحباً، ${currentUser.firstName}`;
    }
}

// ===== AUCTION FUNCTIONS =====
function placeBid(auctionId, currentBid) {
    if (!currentUser) {
        showNotification('يجب تسجيل الدخول أولاً للمشاركة في المزاد', 'error');
        window.location.href = 'Login.aspx';
        return;
    }
    
    const bidAmount = prompt(`أدخل مبلغ المزايدة (أكثر من ${currentBid} ريال):`);
    
    if (bidAmount && parseFloat(bidAmount) > parseFloat(currentBid)) {
        // This would typically make an AJAX call to the server
        showNotification('تم تسجيل مزايدتك بنجاح!', 'success');
        
        // Update the UI (this would be done by the server response)
        setTimeout(() => {
            location.reload();
        }, 2000);
    } else if (bidAmount) {
        showNotification('مبلغ المزايدة يجب أن يكون أكبر من السعر الحالي', 'error');
    }
}

function watchAuction(auctionId) {
    if (!currentUser) {
        showNotification('يجب تسجيل الدخول أولاً', 'error');
        return;
    }
    
    // Add to watchlist
    let watchlist = JSON.parse(localStorage.getItem('watchlist') || '[]');
    if (!watchlist.includes(auctionId)) {
        watchlist.push(auctionId);
        localStorage.setItem('watchlist', JSON.stringify(watchlist));
        showNotification('تم إضافة المزاد إلى قائمة المتابعة', 'success');
    } else {
        showNotification('المزاد موجود بالفعل في قائمة المتابعة', 'info');
    }
}

// ===== SEARCH AND FILTER =====
function initializeSearch() {
    const searchInput = document.getElementById('txtSearch');
    if (searchInput) {
        let searchTimeout;
        
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performSearch(this.value);
            }, 500);
        });
    }
}

function performSearch(query) {
    // This would typically make an AJAX call to filter results
    console.log('Searching for:', query);
}

// ===== UTILITY FUNCTIONS =====
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 0
    }).format(amount);
}

function formatDate(date) {
    return new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(new Date(date));
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// ===== SMOOTH SCROLLING =====
function smoothScrollTo(target) {
    const element = document.querySelector(target);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// ===== LOADING STATES =====
function showLoading(element) {
    if (element) {
        element.classList.add('loading');
        element.disabled = true;
        
        const originalText = element.textContent;
        element.setAttribute('data-original-text', originalText);
        element.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
    }
}

function hideLoading(element) {
    if (element) {
        element.classList.remove('loading');
        element.disabled = false;
        
        const originalText = element.getAttribute('data-original-text');
        if (originalText) {
            element.textContent = originalText;
            element.removeAttribute('data-original-text');
        }
    }
}

// ===== ANIMATION UTILITIES =====
function fadeIn(element, duration = 300) {
    element.style.opacity = 0;
    element.style.display = 'block';
    
    let start = performance.now();
    
    function animate(currentTime) {
        let elapsed = currentTime - start;
        let progress = elapsed / duration;
        
        if (progress < 1) {
            element.style.opacity = progress;
            requestAnimationFrame(animate);
        } else {
            element.style.opacity = 1;
        }
    }
    
    requestAnimationFrame(animate);
}

function fadeOut(element, duration = 300) {
    let start = performance.now();
    let startOpacity = parseFloat(getComputedStyle(element).opacity);
    
    function animate(currentTime) {
        let elapsed = currentTime - start;
        let progress = elapsed / duration;
        
        if (progress < 1) {
            element.style.opacity = startOpacity * (1 - progress);
            requestAnimationFrame(animate);
        } else {
            element.style.opacity = 0;
            element.style.display = 'none';
        }
    }
    
    requestAnimationFrame(animate);
}

// ===== KEYBOARD SHORTCUTS =====
document.addEventListener('keydown', function(e) {
    // Escape key to close modals/menus
    if (e.key === 'Escape') {
        const activeModal = document.querySelector('.modal.active');
        if (activeModal) {
            closeModal(activeModal);
        }
        
        const activeMenu = document.querySelector('.nav-menu.active');
        if (activeMenu) {
            activeMenu.classList.remove('active');
            document.querySelector('.hamburger').classList.remove('active');
        }
    }
    
    // Ctrl+K for search
    if (e.ctrlKey && e.key === 'k') {
        e.preventDefault();
        const searchInput = document.getElementById('txtSearch');
        if (searchInput) {
            searchInput.focus();
        }
    }
});

// ===== ACCESSIBILITY =====
function initializeAccessibility() {
    // Add ARIA labels to interactive elements
    const buttons = document.querySelectorAll('button, .btn');
    buttons.forEach(button => {
        if (!button.getAttribute('aria-label') && !button.textContent.trim()) {
            button.setAttribute('aria-label', 'زر');
        }
    });
    
    // Add focus indicators
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Tab') {
            document.body.classList.add('keyboard-navigation');
        }
    });
    
    document.addEventListener('mousedown', function() {
        document.body.classList.remove('keyboard-navigation');
    });
}

// ===== PERFORMANCE MONITORING =====
function initializePerformanceMonitoring() {
    // Monitor page load time
    window.addEventListener('load', function() {
        const loadTime = performance.now();
        console.log(`Page loaded in ${loadTime.toFixed(2)}ms`);
        
        // Report slow loading times
        if (loadTime > 3000) {
            console.warn('Page load time is slow:', loadTime);
        }
    });
}

// ===== ERROR HANDLING =====
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error);
    
    // Show user-friendly error message for critical errors
    if (e.error && e.error.message) {
        showNotification('حدث خطأ غير متوقع. يرجى إعادة تحميل الصفحة.', 'error');
    }
});

// ===== INITIALIZE ON LOAD =====
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApp);
} else {
    initializeApp();
}

// Initialize additional features
initializeAccessibility();
initializePerformanceMonitoring();
initializeSearch();
