# موقع المزادات الإلكترونية للسيارات
## Car Auction Website

### نظرة عامة
موقع إلكتروني شامل للمزادات الإلكترونية للسيارات مطور باستخدام ASP.NET Web Forms مع قاعدة بيانات SQL Server. يوفر المنصة للمستخدمين إمكانية عرض سياراتهم للبيع في مزادات إلكترونية والمشاركة في مزايدات السيارات الأخرى.

### المميزات الرئيسية

#### للمستخدمين
- **التسجيل وتسجيل الدخول**: نظام آمن لإدارة الحسابات
- **عرض السيارات**: إضافة السيارات للمزاد مع الصور والتفاصيل الكاملة
- **المشاركة في المزادات**: مزايدة على السيارات المعروضة
- **متابعة المزادات**: إضافة المزادات المفضلة لقائمة المتابعة
- **الإشعارات**: تنبيهات فورية عند انتهاء المزادات أو المزايدات الجديدة

#### للإدارة
- **إدارة المزادات**: الموافقة على المزادات وإدارتها
- **إدارة المستخدمين**: مراقبة نشاط المستخدمين
- **التقارير**: إحصائيات شاملة عن المزادات والمبيعات
- **سجل التدقيق**: تتبع جميع العمليات المهمة

### التقنيات المستخدمة

#### Frontend
- **HTML5**: هيكل الصفحات
- **CSS3**: تصميم متجاوب وحديث
- **JavaScript**: تفاعل المستخدم والوظائف الديناميكية
- **Font Awesome**: الأيقونات
- **Responsive Design**: متوافق مع جميع الأجهزة

#### Backend
- **ASP.NET Web Forms 4.8**: إطار العمل الرئيسي
- **C#**: لغة البرمجة
- **SQL Server**: قاعدة البيانات
- **ADO.NET**: الوصول لقاعدة البيانات

#### قاعدة البيانات
- **SQL Server LocalDB**: للتطوير
- **Stored Procedures**: للعمليات المعقدة
- **Views**: لاستعلامات محسنة
- **Triggers**: للتدقيق والمراقبة

### متطلبات النظام

#### للتطوير
- Visual Studio 2022 أو أحدث
- .NET Framework 4.8
- SQL Server LocalDB أو SQL Server Express
- IIS Express (مدمج مع Visual Studio)

#### للإنتاج
- Windows Server 2016 أو أحدث
- IIS 10 أو أحدث
- SQL Server 2016 أو أحدث
- .NET Framework 4.8

### تثبيت وتشغيل المشروع

#### 1. استنساخ المشروع
```bash
git clone [repository-url]
cd car-auction-website
```

#### 2. إعداد قاعدة البيانات
1. افتح SQL Server Management Studio
2. قم بتشغيل ملف `CarAuctionWebsite/App_Data/CreateDatabase.sql`
3. تأكد من إنشاء قاعدة البيانات والجداول بنجاح

#### 3. تحديث سلسلة الاتصال
في ملف `Web.config`، قم بتحديث سلسلة الاتصال:
```xml
<connectionStrings>
    <add name="CarAuctionDB" 
         connectionString="Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\CarAuctionDB.mdf;Integrated Security=True" 
         providerName="System.Data.SqlClient" />
</connectionStrings>
```

#### 4. تشغيل المشروع
1. افتح الحل في Visual Studio 2022
2. اضغط F5 أو Ctrl+F5 لتشغيل المشروع
3. سيتم فتح المتصفح تلقائياً على الصفحة الرئيسية

### هيكل المشروع

```
CarAuctionWebsite/
├── App_Data/
│   └── CreateDatabase.sql          # سكريبت إنشاء قاعدة البيانات
├── css/
│   └── style.css                   # ملف التصميم الرئيسي
├── js/
│   └── main.js                     # ملف JavaScript الرئيسي
├── images/                         # مجلد الصور
├── Properties/
│   └── AssemblyInfo.cs            # معلومات التجميع
├── Default.aspx                    # الصفحة الرئيسية
├── Default.aspx.cs                 # كود الصفحة الرئيسية
├── Login.aspx                      # صفحة تسجيل الدخول
├── Login.aspx.cs                   # كود تسجيل الدخول
├── Register.aspx                   # صفحة التسجيل
├── Register.aspx.cs                # كود التسجيل
├── Auctions.aspx                   # صفحة المزادات
├── AddCar.aspx                     # صفحة إضافة السيارة
├── Web.config                      # ملف التكوين
└── CarAuctionWebsite.csproj        # ملف المشروع
```

### قاعدة البيانات

#### الجداول الرئيسية
- **Users**: بيانات المستخدمين
- **Cars**: بيانات السيارات
- **Auctions**: بيانات المزادات
- **Bids**: المزايدات
- **CarImages**: صور السيارات
- **Watchlist**: قائمة المتابعة
- **Notifications**: الإشعارات
- **AuditLog**: سجل التدقيق

#### العلاقات
- علاقة واحد لمتعدد بين Users و Cars
- علاقة واحد لواحد بين Cars و Auctions
- علاقة واحد لمتعدد بين Auctions و Bids
- علاقة واحد لمتعدد بين Cars و CarImages

### الوظائف الرئيسية

#### إدارة المستخدمين
- تسجيل حساب جديد مع التحقق من البيانات
- تسجيل الدخول مع خيار "تذكرني"
- تشفير كلمات المرور باستخدام SHA256
- إدارة جلسات المستخدمين

#### إدارة المزادات
- إضافة سيارة جديدة للمزاد
- رفع صور متعددة للسيارة
- تحديد مدة المزاد والسعر الابتدائي
- المزايدة التلقائية مع التحقق من الصحة
- إنهاء المزادات تلقائياً عند انتهاء الوقت

#### البحث والتصفية
- البحث بالكلمات المفتاحية
- تصفية حسب الماركة والسنة والسعر
- ترتيب النتائج حسب معايير مختلفة
- عرض النتائج مع الصفحات

### الأمان

#### تشفير البيانات
- تشفير كلمات المرور باستخدام SHA256 مع Salt
- حماية من SQL Injection باستخدام Parameters
- التحقق من صحة البيانات في الخادم والعميل

#### إدارة الجلسات
- انتهاء صلاحية الجلسات تلقائياً
- حماية الصفحات الحساسة
- تسجيل خروج آمن

#### سجل التدقيق
- تسجيل جميع العمليات المهمة
- تتبع تغييرات البيانات
- مراقبة نشاط المستخدمين

### الاختبار

#### بيانات تجريبية
يحتوي ملف قاعدة البيانات على بيانات تجريبية للاختبار:
- 3 مستخدمين تجريبيين
- 3 سيارات نموذجية
- 3 مزادات نشطة
- مزايدات تجريبية

#### اختبار الوظائف
1. تسجيل مستخدم جديد
2. تسجيل الدخول
3. إضافة سيارة جديدة
4. المشاركة في مزاد
5. متابعة المزادات

### التطوير المستقبلي

#### مميزات مقترحة
- نظام دفع إلكتروني
- تطبيق موبايل
- إشعارات فورية (Push Notifications)
- نظام تقييم المستخدمين
- دردشة مباشرة
- تقارير متقدمة
- API للتطبيقات الخارجية

#### تحسينات تقنية
- ترقية إلى ASP.NET Core
- استخدام Entity Framework
- تطبيق نمط MVC
- إضافة Unit Tests
- تحسين الأداء
- إضافة CDN للصور

### الدعم والمساعدة

#### التوثيق
- تعليقات شاملة في الكود
- توثيق قاعدة البيانات
- دليل المستخدم
- دليل المطور

#### استكشاف الأخطاء
- رسائل خطأ واضحة
- سجل الأخطاء
- وضع التطوير للتشخيص

### الترخيص
هذا المشروع مطور لأغراض تعليمية وتجريبية. يمكن استخدامه وتطويره بحرية.

### المساهمة
نرحب بالمساهمات لتطوير المشروع. يرجى إنشاء Pull Request أو فتح Issue للمناقشة.

### الاتصال
للاستفسارات والدعم الفني، يرجى التواصل عبر:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966 50 123 4567

---

**ملاحظة**: هذا المشروع تم تطويره باستخدام أحدث معايير الأمان والأداء لضمان تجربة مستخدم ممتازة وموثوقة.
