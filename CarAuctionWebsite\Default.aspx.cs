using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace CarAuctionWebsite
{
    public partial class Default : System.Web.UI.Page
    {
        private string connectionString = ConfigurationManager.ConnectionStrings["CarAuctionDB"].ConnectionString;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                CheckUserSession();
                LoadLatestAuctions();
                LoadStatistics();
            }
        }

        private void CheckUserSession()
        {
            if (Session["UserId"] != null)
            {
                // User is logged in
                lnkLogin.Visible = false;
                lnkRegister.Visible = false;
                userMenu.Visible = true;
                
                string firstName = Session["FirstName"]?.ToString() ?? "المستخدم";
                lblWelcome.Text = $"مرحباً، {firstName}";
            }
            else
            {
                // User is not logged in
                lnkLogin.Visible = true;
                lnkRegister.Visible = true;
                userMenu.Visible = false;
            }
        }

        private void LoadLatestAuctions()
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    string query = @"
                        SELECT TOP 6 
                            a.AuctionId,
                            c.Brand + ' ' + c.Model + ' ' + CAST(c.Year AS VARCHAR) AS CarModel,
                            c.Year,
                            c.Mileage,
                            a.CurrentBid,
                            a.EndDateTime,
                            a.Status,
                            ISNULL(c.ImageUrl, 'images/default-car.jpg') AS ImageUrl,
                            (SELECT COUNT(*) FROM Bids b WHERE b.AuctionId = a.AuctionId) AS BidCount
                        FROM Auctions a
                        INNER JOIN Cars c ON a.CarId = c.CarId
                        WHERE a.Status = 'Active' AND a.EndDateTime > GETDATE()
                        ORDER BY a.CreatedDate DESC";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            List<object> auctions = new List<object>();
                            
                            while (reader.Read())
                            {
                                DateTime endDateTime = Convert.ToDateTime(reader["EndDateTime"]);
                                TimeSpan timeLeft = endDateTime - DateTime.Now;
                                
                                string timeLeftText = FormatTimeLeft(timeLeft);
                                string statusClass = GetStatusClass(reader["Status"].ToString(), timeLeft);
                                
                                auctions.Add(new
                                {
                                    AuctionId = reader["AuctionId"],
                                    CarModel = reader["CarModel"],
                                    Year = reader["Year"],
                                    Mileage = FormatMileage(Convert.ToInt32(reader["Mileage"])),
                                    CurrentBid = FormatCurrency(Convert.ToDecimal(reader["CurrentBid"])),
                                    TimeLeft = timeLeftText,
                                    Status = GetStatusText(reader["Status"].ToString(), timeLeft),
                                    StatusClass = statusClass,
                                    ImageUrl = reader["ImageUrl"],
                                    BidCount = reader["BidCount"],
                                    EndDateTime = endDateTime.ToString("yyyy-MM-ddTHH:mm:ss")
                                });
                            }
                            
                            rptLatestAuctions.DataSource = auctions;
                            rptLatestAuctions.DataBind();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error
                System.Diagnostics.Debug.WriteLine($"Error loading auctions: {ex.Message}");
                
                // Show empty state or error message
                rptLatestAuctions.DataSource = new List<object>();
                rptLatestAuctions.DataBind();
            }
        }

        private void LoadStatistics()
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    
                    // Total active auctions
                    string activeAuctionsQuery = "SELECT COUNT(*) FROM Auctions WHERE Status = 'Active' AND EndDateTime > GETDATE()";
                    using (SqlCommand cmd = new SqlCommand(activeAuctionsQuery, conn))
                    {
                        lblTotalAuctions.Text = cmd.ExecuteScalar().ToString();
                    }
                    
                    // Total registered users
                    string usersQuery = "SELECT COUNT(*) FROM Users WHERE IsActive = 1";
                    using (SqlCommand cmd = new SqlCommand(usersQuery, conn))
                    {
                        lblTotalUsers.Text = cmd.ExecuteScalar().ToString();
                    }
                    
                    // Total cars
                    string carsQuery = "SELECT COUNT(*) FROM Cars";
                    using (SqlCommand cmd = new SqlCommand(carsQuery, conn))
                    {
                        lblTotalCars.Text = cmd.ExecuteScalar().ToString();
                    }
                    
                    // Completed auctions
                    string completedQuery = "SELECT COUNT(*) FROM Auctions WHERE Status = 'Completed'";
                    using (SqlCommand cmd = new SqlCommand(completedQuery, conn))
                    {
                        lblCompletedAuctions.Text = cmd.ExecuteScalar().ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading statistics: {ex.Message}");
                
                // Set default values
                lblTotalAuctions.Text = "0";
                lblTotalUsers.Text = "0";
                lblTotalCars.Text = "0";
                lblCompletedAuctions.Text = "0";
            }
        }

        protected void lnkLogout_Click(object sender, EventArgs e)
        {
            // Clear session
            Session.Clear();
            Session.Abandon();
            
            // Redirect to home page
            Response.Redirect("Default.aspx");
        }

        private string FormatTimeLeft(TimeSpan timeLeft)
        {
            if (timeLeft.TotalDays >= 1)
            {
                return $"{(int)timeLeft.TotalDays} يوم {timeLeft.Hours} ساعة";
            }
            else if (timeLeft.TotalHours >= 1)
            {
                return $"{timeLeft.Hours} ساعة {timeLeft.Minutes} دقيقة";
            }
            else if (timeLeft.TotalMinutes >= 1)
            {
                return $"{timeLeft.Minutes} دقيقة";
            }
            else if (timeLeft.TotalSeconds > 0)
            {
                return "أقل من دقيقة";
            }
            else
            {
                return "انتهى";
            }
        }

        private string GetStatusClass(string status, TimeSpan timeLeft)
        {
            if (status != "Active")
                return "completed";
            
            if (timeLeft.TotalHours <= 24)
                return "ending-soon";
            
            return "active";
        }

        private string GetStatusText(string status, TimeSpan timeLeft)
        {
            if (status != "Active")
                return "مكتمل";
            
            if (timeLeft.TotalSeconds <= 0)
                return "انتهى";
            
            if (timeLeft.TotalHours <= 24)
                return "ينتهي قريباً";
            
            return "نشط";
        }

        private string FormatCurrency(decimal amount)
        {
            return amount.ToString("N0") + " ريال";
        }

        private string FormatMileage(int mileage)
        {
            return mileage.ToString("N0");
        }
    }
}
