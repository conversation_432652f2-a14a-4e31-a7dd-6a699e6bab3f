using System;
using System.Configuration;
using System.Data.SqlClient;
using System.Security.Cryptography;
using System.Text;
using System.Web.UI;

namespace CarAuctionWebsite
{
    public partial class Login : System.Web.UI.Page
    {
        private string connectionString = ConfigurationManager.ConnectionStrings["CarAuctionDB"].ConnectionString;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // Check if user is already logged in
                if (Session["UserId"] != null)
                {
                    Response.Redirect("Default.aspx");
                }
                
                // Focus on username field
                txtUsername.Focus();
            }
        }

        protected void btnLogin_Click(object sender, EventArgs e)
        {
            if (Page.IsValid)
            {
                string username = txtUsername.Text.Trim();
                string password = txtPassword.Text;
                
                if (ValidateUser(username, password))
                {
                    // Successful login
                    string returnUrl = Request.QueryString["ReturnUrl"];
                    if (!string.IsNullOrEmpty(returnUrl))
                    {
                        Response.Redirect(returnUrl);
                    }
                    else
                    {
                        Response.Redirect("Default.aspx");
                    }
                }
                else
                {
                    ShowMessage("اسم المستخدم أو كلمة المرور غير صحيحة", "error");
                }
            }
        }

        private bool ValidateUser(string username, string password)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    string query = @"
                        SELECT UserId, FirstName, LastName, Email, PasswordHash, IsActive 
                        FROM Users 
                        WHERE Username = @Username AND IsActive = 1";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@Username", username);
                        
                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                string storedHash = reader["PasswordHash"].ToString();
                                
                                // Verify password
                                if (VerifyPassword(password, storedHash))
                                {
                                    // Set session variables
                                    Session["UserId"] = reader["UserId"];
                                    Session["Username"] = username;
                                    Session["FirstName"] = reader["FirstName"];
                                    Session["LastName"] = reader["LastName"];
                                    Session["Email"] = reader["Email"];
                                    Session["LoginTime"] = DateTime.Now;
                                    
                                    // Update last login time
                                    UpdateLastLogin(Convert.ToInt32(reader["UserId"]));
                                    
                                    // Set remember me cookie if checked
                                    if (chkRememberMe.Checked)
                                    {
                                        SetRememberMeCookie(username);
                                    }
                                    
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Login error: {ex.Message}");
                ShowMessage("حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.", "error");
            }
            
            return false;
        }

        private bool VerifyPassword(string password, string hash)
        {
            try
            {
                // For this example, we'll use a simple SHA256 hash
                // In production, use a proper password hashing library like BCrypt
                string hashedPassword = HashPassword(password);
                return hashedPassword == hash;
            }
            catch
            {
                return false;
            }
        }

        private string HashPassword(string password)
        {
            using (SHA256 sha256Hash = SHA256.Create())
            {
                byte[] bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(password + "CarAuctionSalt"));
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < bytes.Length; i++)
                {
                    builder.Append(bytes[i].ToString("x2"));
                }
                return builder.ToString();
            }
        }

        private void UpdateLastLogin(int userId)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    string query = "UPDATE Users SET LastLoginDate = GETDATE() WHERE UserId = @UserId";
                    
                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@UserId", userId);
                        
                        conn.Open();
                        cmd.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating last login: {ex.Message}");
            }
        }

        private void SetRememberMeCookie(string username)
        {
            try
            {
                // Create a remember me cookie that expires in 30 days
                System.Web.HttpCookie rememberCookie = new System.Web.HttpCookie("RememberMe", username);
                rememberCookie.Expires = DateTime.Now.AddDays(30);
                rememberCookie.HttpOnly = true;
                rememberCookie.Secure = Request.IsSecureConnection;
                
                Response.Cookies.Add(rememberCookie);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error setting remember me cookie: {ex.Message}");
            }
        }

        private void ShowMessage(string message, string type)
        {
            lblMessage.Text = message;
            pnlMessage.CssClass = $"message-panel {type}";
            pnlMessage.Visible = true;
        }

        protected void Page_PreRender(object sender, EventArgs e)
        {
            // Check for remember me cookie
            if (!IsPostBack && Request.Cookies["RememberMe"] != null)
            {
                string rememberedUsername = Request.Cookies["RememberMe"].Value;
                if (!string.IsNullOrEmpty(rememberedUsername))
                {
                    txtUsername.Text = rememberedUsername;
                    chkRememberMe.Checked = true;
                    txtPassword.Focus();
                }
            }
        }
    }
}
