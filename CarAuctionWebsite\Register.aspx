<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Register.aspx.cs" Inherits="CarAuctionWebsite.Register" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ar" dir="rtl">
<head runat="server">
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>التسجيل - مزادات السيارات</title>
    <link href="css/style.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
</head>
<body class="auth-page">
    <form id="form1" runat="server">
        <!-- Header -->
        <header class="header">
            <nav class="navbar">
                <div class="nav-container">
                    <div class="nav-logo">
                        <a href="Default.aspx">
                            <h2><i class="fas fa-car"></i> مزادات السيارات</h2>
                        </a>
                    </div>
                    <ul class="nav-menu">
                        <li class="nav-item">
                            <a href="Default.aspx" class="nav-link">الرئيسية</a>
                        </li>
                        <li class="nav-item">
                            <a href="Auctions.aspx" class="nav-link">المزادات</a>
                        </li>
                        <li class="nav-item">
                            <a href="Login.aspx" class="nav-link">تسجيل الدخول</a>
                        </li>
                    </ul>
                </div>
            </nav>
        </header>

        <!-- Register Section -->
        <section class="auth-section">
            <div class="auth-container">
                <div class="auth-card register-card">
                    <div class="auth-header">
                        <h2><i class="fas fa-user-plus"></i> إنشاء حساب جديد</h2>
                        <p>انضم إلينا واستمتع بأفضل تجربة مزادات السيارات</p>
                    </div>

                    <div class="auth-form">
                        <!-- Error/Success Messages -->
                        <asp:Panel ID="pnlMessage" runat="server" Visible="false" CssClass="message-panel">
                            <asp:Label ID="lblMessage" runat="server"></asp:Label>
                        </asp:Panel>

                        <div class="form-row">
                            <!-- First Name -->
                            <div class="form-group half-width">
                                <label for="txtFirstName">
                                    <i class="fas fa-user"></i> الاسم الأول
                                </label>
                                <asp:TextBox ID="txtFirstName" runat="server" CssClass="form-control" 
                                    placeholder="الاسم الأول" MaxLength="50"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvFirstName" runat="server" 
                                    ControlToValidate="txtFirstName" 
                                    ErrorMessage="الاسم الأول مطلوب" 
                                    CssClass="error-message" 
                                    Display="Dynamic"></asp:RequiredFieldValidator>
                            </div>

                            <!-- Last Name -->
                            <div class="form-group half-width">
                                <label for="txtLastName">
                                    <i class="fas fa-user"></i> الاسم الأخير
                                </label>
                                <asp:TextBox ID="txtLastName" runat="server" CssClass="form-control" 
                                    placeholder="الاسم الأخير" MaxLength="50"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvLastName" runat="server" 
                                    ControlToValidate="txtLastName" 
                                    ErrorMessage="الاسم الأخير مطلوب" 
                                    CssClass="error-message" 
                                    Display="Dynamic"></asp:RequiredFieldValidator>
                            </div>
                        </div>

                        <!-- Username -->
                        <div class="form-group">
                            <label for="txtUsername">
                                <i class="fas fa-at"></i> اسم المستخدم
                            </label>
                            <asp:TextBox ID="txtUsername" runat="server" CssClass="form-control" 
                                placeholder="اختر اسم المستخدم" MaxLength="50"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvUsername" runat="server" 
                                ControlToValidate="txtUsername" 
                                ErrorMessage="اسم المستخدم مطلوب" 
                                CssClass="error-message" 
                                Display="Dynamic"></asp:RequiredFieldValidator>
                            <asp:RegularExpressionValidator ID="revUsername" runat="server" 
                                ControlToValidate="txtUsername" 
                                ValidationExpression="^[a-zA-Z0-9_]{3,50}$" 
                                ErrorMessage="اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط (3-50 حرف)" 
                                CssClass="error-message" 
                                Display="Dynamic"></asp:RegularExpressionValidator>
                        </div>

                        <!-- Email -->
                        <div class="form-group">
                            <label for="txtEmail">
                                <i class="fas fa-envelope"></i> البريد الإلكتروني
                            </label>
                            <asp:TextBox ID="txtEmail" runat="server" TextMode="Email" CssClass="form-control" 
                                placeholder="<EMAIL>" MaxLength="100"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvEmail" runat="server" 
                                ControlToValidate="txtEmail" 
                                ErrorMessage="البريد الإلكتروني مطلوب" 
                                CssClass="error-message" 
                                Display="Dynamic"></asp:RequiredFieldValidator>
                            <asp:RegularExpressionValidator ID="revEmail" runat="server" 
                                ControlToValidate="txtEmail" 
                                ValidationExpression="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$" 
                                ErrorMessage="صيغة البريد الإلكتروني غير صحيحة" 
                                CssClass="error-message" 
                                Display="Dynamic"></asp:RegularExpressionValidator>
                        </div>

                        <!-- Phone -->
                        <div class="form-group">
                            <label for="txtPhone">
                                <i class="fas fa-phone"></i> رقم الهاتف
                            </label>
                            <asp:TextBox ID="txtPhone" runat="server" CssClass="form-control" 
                                placeholder="05xxxxxxxx" MaxLength="15"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvPhone" runat="server" 
                                ControlToValidate="txtPhone" 
                                ErrorMessage="رقم الهاتف مطلوب" 
                                CssClass="error-message" 
                                Display="Dynamic"></asp:RequiredFieldValidator>
                            <asp:RegularExpressionValidator ID="revPhone" runat="server" 
                                ControlToValidate="txtPhone" 
                                ValidationExpression="^(05|5)[0-9]{8}$" 
                                ErrorMessage="رقم الهاتف غير صحيح (يجب أن يبدأ بـ 05)" 
                                CssClass="error-message" 
                                Display="Dynamic"></asp:RegularExpressionValidator>
                        </div>

                        <div class="form-row">
                            <!-- Password -->
                            <div class="form-group half-width">
                                <label for="txtPassword">
                                    <i class="fas fa-lock"></i> كلمة المرور
                                </label>
                                <div class="password-input">
                                    <asp:TextBox ID="txtPassword" runat="server" TextMode="Password" 
                                        CssClass="form-control" placeholder="كلمة المرور" MaxLength="100"></asp:TextBox>
                                    <span class="password-toggle" onclick="togglePassword('txtPassword')">
                                        <i class="fas fa-eye"></i>
                                    </span>
                                </div>
                                <asp:RequiredFieldValidator ID="rfvPassword" runat="server" 
                                    ControlToValidate="txtPassword" 
                                    ErrorMessage="كلمة المرور مطلوبة" 
                                    CssClass="error-message" 
                                    Display="Dynamic"></asp:RequiredFieldValidator>
                                <asp:RegularExpressionValidator ID="revPassword" runat="server" 
                                    ControlToValidate="txtPassword" 
                                    ValidationExpression="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$" 
                                    ErrorMessage="كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل مع حرف كبير وصغير ورقم" 
                                    CssClass="error-message" 
                                    Display="Dynamic"></asp:RegularExpressionValidator>
                            </div>

                            <!-- Confirm Password -->
                            <div class="form-group half-width">
                                <label for="txtConfirmPassword">
                                    <i class="fas fa-lock"></i> تأكيد كلمة المرور
                                </label>
                                <div class="password-input">
                                    <asp:TextBox ID="txtConfirmPassword" runat="server" TextMode="Password" 
                                        CssClass="form-control" placeholder="تأكيد كلمة المرور" MaxLength="100"></asp:TextBox>
                                    <span class="password-toggle" onclick="togglePassword('txtConfirmPassword')">
                                        <i class="fas fa-eye"></i>
                                    </span>
                                </div>
                                <asp:RequiredFieldValidator ID="rfvConfirmPassword" runat="server" 
                                    ControlToValidate="txtConfirmPassword" 
                                    ErrorMessage="تأكيد كلمة المرور مطلوب" 
                                    CssClass="error-message" 
                                    Display="Dynamic"></asp:RequiredFieldValidator>
                                <asp:CompareValidator ID="cvPassword" runat="server" 
                                    ControlToValidate="txtConfirmPassword" 
                                    ControlToCompare="txtPassword" 
                                    ErrorMessage="كلمة المرور غير متطابقة" 
                                    CssClass="error-message" 
                                    Display="Dynamic"></asp:CompareValidator>
                            </div>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="form-group checkbox-group">
                            <asp:CheckBox ID="chkTerms" runat="server" CssClass="form-checkbox" />
                            <label for="chkTerms">
                                أوافق على <a href="Terms.aspx" target="_blank">الشروط والأحكام</a> و 
                                <a href="Privacy.aspx" target="_blank">سياسة الخصوصية</a>
                            </label>
                            <asp:CustomValidator ID="cvTerms" runat="server" 
                                ErrorMessage="يجب الموافقة على الشروط والأحكام" 
                                CssClass="error-message" 
                                Display="Dynamic" 
                                ClientValidationFunction="validateTerms"></asp:CustomValidator>
                        </div>

                        <!-- Register Button -->
                        <div class="form-group">
                            <asp:Button ID="btnRegister" runat="server" Text="إنشاء الحساب" 
                                CssClass="btn btn-primary btn-full" OnClick="btnRegister_Click" />
                        </div>

                        <!-- Divider -->
                        <div class="auth-divider">
                            <span>أو</span>
                        </div>

                        <!-- Login Link -->
                        <div class="form-group text-center">
                            <p>لديك حساب بالفعل؟ 
                                <a href="Login.aspx" class="auth-link">سجل دخولك</a>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Side Image/Info -->
                <div class="auth-side">
                    <div class="auth-side-content">
                        <h3>انضم إلى مجتمعنا!</h3>
                        <p>سجل الآن واستمتع بأفضل تجربة مزادات السيارات الإلكترونية</p>
                        <div class="auth-features">
                            <div class="feature-item">
                                <i class="fas fa-shield-alt"></i>
                                <span>حماية كاملة لبياناتك</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-gavel"></i>
                                <span>مزادات شفافة وعادلة</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-support"></i>
                                <span>دعم فني على مدار الساعة</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-gift"></i>
                                <span>عروض حصرية للأعضاء</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="footer">
            <div class="container">
                <div class="footer-bottom">
                    <p>&copy; 2024 مزادات السيارات. جميع الحقوق محفوظة.</p>
                </div>
            </div>
        </footer>
    </form>

    <script src="js/main.js"></script>
    <script>
        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const icon = event.target;
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        function validateTerms(sender, args) {
            const checkbox = document.getElementById('<%= chkTerms.ClientID %>');
            args.IsValid = checkbox.checked;
        }
    </script>
</body>
</html>
